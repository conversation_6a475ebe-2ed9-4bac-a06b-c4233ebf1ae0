#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成内嵌SVG数据的HTML文件
将所有SVG文件内容直接嵌入到HTML中，创建真正的单文件版本
"""

import os
import json
from pathlib import Path

def read_svg_file(file_path):
    """读取SVG文件内容"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        return content
    except Exception as e:
        print(f"读取文件 {file_path} 失败: {e}")
        return None

def escape_js_string(content):
    """转义JavaScript字符串中的特殊字符"""
    if not content:
        return ""
    
    # 替换反斜杠、引号和换行符
    content = content.replace('\\', '\\\\')
    content = content.replace('`', '\\`')
    content = content.replace('${', '\\${')
    return content

def generate_html():
    """生成包含内嵌SVG的HTML文件"""
    
    # SVG文件映射（按新的顺序）
    svg_files = {
        'demand': '人员需求申请流程-活动图/人员需求申请流程-活动图.svg',
        'recruitment': '招聘与录用流程-活动图/招聘与录用流程-活动图.svg',
        'training': '培训管理流程-活动图/培训管理流程-活动图.svg',
        'attendance': '考勤与排班流程-活动图/考勤与排班流程-活动图.svg',
        'performance': '绩效管理流程-活动图/绩效管理流程-活动图.svg',
        'salary': '薪酬福利管理流程-活动图/薪酬福利管理流程-活动图.svg',
        'transfer': '员工职位调动及调薪-活动图/员工职位调动及调薪-活动图.svg',
        'resignation': '员工辞职或解雇流程-活动图/员工辞职或解雇流程-活动图.svg'
    }
    
    # 读取所有SVG文件
    svg_data = {}
    for key, file_path in svg_files.items():
        if os.path.exists(file_path):
            content = read_svg_file(file_path)
            if content:
                svg_data[key] = escape_js_string(content)
                print(f"✓ 成功读取: {file_path}")
            else:
                svg_data[key] = ""
                print(f"✗ 读取失败: {file_path}")
        else:
            svg_data[key] = ""
            print(f"✗ 文件不存在: {file_path}")
    
    # HTML模板
    html_template = '''<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>人事流程图展示系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background: #f5f5f5;
            height: 100vh;
            overflow: hidden;
        }

        .container {
            width: 100%;
            height: 100vh;
            background: white;
            display: flex;
            flex-direction: column;
        }

        .tab-container {
            background: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
            flex-shrink: 0;
            display: flex;
            align-items: center;
        }

        .tab-nav {
            display: flex;
            overflow-x: auto;
            padding: 0 20px;
            flex: 1;
        }

        .global-controls {
            padding: 0 20px;
            display: flex;
            align-items: center;
            border-left: 1px solid #dee2e6;
        }

        .tab-button {
            background: none;
            border: none;
            padding: 15px 20px;
            cursor: pointer;
            font-size: 14px;
            font-weight: 500;
            color: #6c757d;
            border-bottom: 3px solid transparent;
            transition: all 0.3s ease;
            white-space: nowrap;
            min-width: 120px;
        }

        .tab-button:hover {
            color: #495057;
            background: rgba(0,0,0,0.05);
        }

        .tab-button.active {
            color: #007bff;
            border-bottom-color: #007bff;
            background: white;
        }

        .content-area {
            flex: 1;
            display: flex;
            flex-direction: column;
            overflow: hidden;
        }

        .tab-content {
            display: none;
            animation: fadeIn 0.3s ease-in;
            flex: 1;
            overflow: hidden;
        }

        .tab-content.active {
            display: flex;
            flex-direction: column;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(10px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .export-section {
            padding: 10px 20px;
            text-align: right;
            background: #f8f9fa;
            border-bottom: 1px solid #dee2e6;
            flex-shrink: 0;
        }

        .quality-control {
            display: flex;
            align-items: center;
            gap: 5px;
            font-size: 12px;
            color: #666;
        }

        .quality-input {
            width: 50px;
            padding: 4px 6px;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 12px;
            text-align: center;
        }

        .export-btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
        }

        .export-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6);
        }

        .export-btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .svg-container {
            flex: 1;
            background: white;
            overflow: auto;
            padding: 20px;
        }

        .svg-container svg {
            display: block;
            margin: 0 auto;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            /* 保持原始尺寸，不进行缩放 */
        }

        .loading {
            text-align: center;
            padding: 50px;
            color: #6c757d;
        }

        .error {
            color: #dc3545;
            text-align: center;
            padding: 20px;
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 5px;
            margin: 20px 0;
        }

        @media (max-width: 768px) {
            .tab-button {
                min-width: 100px;
                padding: 12px 15px;
                font-size: 12px;
            }

            .export-section {
                padding: 8px 15px;
            }

            .svg-container {
                padding: 10px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="tab-container">
            <div class="tab-nav">
                <button class="tab-button active" data-tab="demand">人员需求申请</button>
                <button class="tab-button" data-tab="recruitment">招聘与录用</button>
                <button class="tab-button" data-tab="training">培训管理</button>
                <button class="tab-button" data-tab="attendance">考勤与排班</button>
                <button class="tab-button" data-tab="performance">绩效管理</button>
                <button class="tab-button" data-tab="salary">薪酬福利管理</button>
                <button class="tab-button" data-tab="transfer">员工职位调动及调薪</button>
                <button class="tab-button" data-tab="resignation">员工辞职或解雇</button>
            </div>
            <div class="global-controls">
                <div class="scale-control">
                    <label>显示比例:</label>
                    <input type="range" class="scale-slider" value="0.75" min="0.2" max="1" step="0.05" id="svg-scale">
                    <span id="scale-value">75%</span>
                </div>
                <div class="quality-control">
                    <label>导出清晰度:</label>
                    <input type="number" class="quality-input" value="2" min="1" max="5" step="1" id="global-quality">
                    <span>倍</span>
                </div>
                <button class="export-btn" onclick="exportCurrentTab()" id="global-export-btn">导出PDF</button>
            </div>
        </div>

        <div class="content-area">
            <!-- 人员需求申请 -->
            <div class="tab-content active" id="demand">
                <div class="export-section">
                    <button class="export-btn" onclick="exportToPDF('demand', '人员需求申请流程')">导出PDF</button>
                </div>
                <div class="svg-container" id="demand-svg"></div>
            </div>

            <!-- 招聘与录用 -->
            <div class="tab-content" id="recruitment">
                <div class="export-section">
                    <button class="export-btn" onclick="exportToPDF('recruitment', '招聘与录用流程')">导出PDF</button>
                </div>
                <div class="svg-container" id="recruitment-svg"></div>
            </div>

            <!-- 培训管理 -->
            <div class="tab-content" id="training">
                <div class="export-section">
                    <button class="export-btn" onclick="exportToPDF('training', '培训管理流程')">导出PDF</button>
                </div>
                <div class="svg-container" id="training-svg"></div>
            </div>

            <!-- 考勤与排班 -->
            <div class="tab-content" id="attendance">
                <div class="export-section">
                    <button class="export-btn" onclick="exportToPDF('attendance', '考勤与排班流程')">导出PDF</button>
                </div>
                <div class="svg-container" id="attendance-svg"></div>
            </div>

            <!-- 绩效管理 -->
            <div class="tab-content" id="performance">
                <div class="export-section">
                    <button class="export-btn" onclick="exportToPDF('performance', '绩效管理流程')">导出PDF</button>
                </div>
                <div class="svg-container" id="performance-svg"></div>
            </div>

            <!-- 薪酬福利管理 -->
            <div class="tab-content" id="salary">
                <div class="export-section">
                    <button class="export-btn" onclick="exportToPDF('salary', '薪酬福利管理流程')">导出PDF</button>
                </div>
                <div class="svg-container" id="salary-svg"></div>
            </div>

            <!-- 员工职位调动及调薪 -->
            <div class="tab-content" id="transfer">
                <div class="export-section">
                    <button class="export-btn" onclick="exportToPDF('transfer', '员工职位调动及调薪流程')">导出PDF</button>
                </div>
                <div class="svg-container" id="transfer-svg"></div>
            </div>

            <!-- 员工辞职或解雇 -->
            <div class="tab-content" id="resignation">
                <div class="export-section">
                    <button class="export-btn" onclick="exportToPDF('resignation', '员工辞职或解雇流程')">导出PDF</button>
                </div>
                <div class="svg-container" id="resignation-svg"></div>
            </div>
        </div>
    </div>

    <!-- PDF导出库 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://unpkg.com/canvg/lib/umd.js"></script>

    <script>
        // 内嵌的SVG数据
        const svgData = {SVG_DATA_PLACEHOLDER};

        // Tab 切换功能
        function initTabs() {
            const tabButtons = document.querySelectorAll('.tab-button');
            const tabContents = document.querySelectorAll('.tab-content');

            tabButtons.forEach(button => {
                button.addEventListener('click', () => {
                    const targetTab = button.getAttribute('data-tab');
                    
                    // 移除所有活动状态
                    tabButtons.forEach(btn => btn.classList.remove('active'));
                    tabContents.forEach(content => content.classList.remove('active'));
                    
                    // 添加活动状态
                    button.classList.add('active');
                    document.getElementById(targetTab).classList.add('active');
                    
                    // 加载对应的 SVG
                    loadSVG(targetTab);
                });
            });
        }

        // 加载 SVG 内容
        function loadSVG(tabName) {
            const container = document.getElementById(`${tabName}-svg`);
            
            if (svgData[tabName] && svgData[tabName].trim()) {
                container.innerHTML = svgData[tabName];
            } else {
                container.innerHTML = `
                    <div class="error">
                        <h3>SVG 内容未找到</h3>
                        <p>流程图 "${tabName}" 的内容暂时无法显示</p>
                    </div>
                `;
            }
        }

        // PDF 导出功能 - 高质量图片导出
        async function exportToPDF(tabName, fileName) {
            const button = event.target;
            const originalText = button.textContent;

            try {
                button.textContent = '导出中...';
                button.disabled = true;

                const svgContainer = document.getElementById(`${tabName}-svg`);
                const svgElement = svgContainer.querySelector('svg');

                if (!svgElement) {
                    throw new Error('未找到可导出的图形内容');
                }

                // 检查必要的库是否加载
                if (typeof window.jspdf === 'undefined') {
                    throw new Error('jsPDF 库未加载，请检查网络连接');
                }

                // 获取SVG的尺寸信息
                let svgWidth, svgHeight;

                // 尝试从不同属性获取SVG尺寸
                if (svgElement.viewBox && svgElement.viewBox.baseVal) {
                    svgWidth = svgElement.viewBox.baseVal.width;
                    svgHeight = svgElement.viewBox.baseVal.height;
                } else if (svgElement.getAttribute('width') && svgElement.getAttribute('height')) {
                    svgWidth = parseFloat(svgElement.getAttribute('width').replace(/[^\\d.]/g, ''));
                    svgHeight = parseFloat(svgElement.getAttribute('height').replace(/[^\\d.]/g, ''));
                } else {
                    // 使用实际渲染尺寸
                    const rect = svgElement.getBoundingClientRect();
                    svgWidth = rect.width || 800;
                    svgHeight = rect.height || 600;
                }

                // 创建PDF，根据SVG尺寸选择页面方向和格式
                const { jsPDF } = window.jspdf;
                const isLandscape = svgWidth > svgHeight;

                // 根据SVG尺寸选择合适的页面格式
                let format = 'a4';
                if (svgWidth > 1200 || svgHeight > 1200) {
                    format = 'a3';
                } else if (svgWidth > 2000 || svgHeight > 2000) {
                    format = 'a2';
                }

                const pdf = new jsPDF({
                    orientation: isLandscape ? 'landscape' : 'portrait',
                    unit: 'pt',
                    format: format
                });

                // 获取PDF页面尺寸
                const pageWidth = pdf.internal.pageSize.getWidth();
                const pageHeight = pdf.internal.pageSize.getHeight();
                const margin = 20; // 边距

                // 计算缩放比例，保持宽高比
                const availableWidth = pageWidth - (margin * 2);
                const availableHeight = pageHeight - (margin * 2);
                const scaleX = availableWidth / svgWidth;
                const scaleY = availableHeight / svgHeight;
                const scale = Math.min(scaleX, scaleY, 1); // 不放大，只缩小

                // 计算居中位置
                const scaledWidth = svgWidth * scale;
                const scaledHeight = svgHeight * scale;
                const x = (pageWidth - scaledWidth) / 2;
                const y = (pageHeight - scaledHeight) / 2;

                // 获取全局设置的像素密度
                const globalQualityInput = document.getElementById('global-quality');
                const pixelRatio = globalQualityInput ? parseInt(globalQualityInput.value) || 2 : 2;

                // 使用canvg将SVG转换为高质量Canvas
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');

                // 设置用户指定分辨率的Canvas
                canvas.width = scaledWidth * pixelRatio;
                canvas.height = scaledHeight * pixelRatio;

                // 获取SVG字符串
                const svgString = new XMLSerializer().serializeToString(svgElement);

                // 使用canvg渲染SVG到Canvas
                if (typeof canvg !== 'undefined') {
                    // 使用canvg库进行高质量渲染
                    const v = canvg.Canvg.fromString(ctx, svgString, {
                        ignoreDimensions: true,
                        scaleWidth: canvas.width,
                        scaleHeight: canvas.height,
                        enableRedraw: false
                    });

                    v.start();

                    // 转换为图片数据
                    const imgData = canvas.toDataURL('image/png', 1.0);

                    // 添加到PDF
                    pdf.addImage(imgData, 'PNG', x, y, scaledWidth, scaledHeight);

                } else {
                    // 备用方案：使用传统方法
                    console.warn('canvg库未加载，使用备用方案');

                    // 缩放上下文
                    ctx.scale(pixelRatio, pixelRatio);
                    ctx.fillStyle = 'white';
                    ctx.fillRect(0, 0, scaledWidth, scaledHeight);

                    // 创建Image对象加载SVG
                    const img = new Image();
                    const svgBlob = new Blob([svgString], { type: 'image/svg+xml;charset=utf-8' });
                    const svgUrl = URL.createObjectURL(svgBlob);

                    img.onload = function() {
                        ctx.drawImage(img, 0, 0, scaledWidth, scaledHeight);
                        const imgData = canvas.toDataURL('image/png', 1.0);
                        pdf.addImage(imgData, 'PNG', x, y, scaledWidth, scaledHeight);

                        // 添加标题和信息
                        addPDFMetadata();

                        URL.revokeObjectURL(svgUrl);
                    };

                    img.onerror = function() {
                        alert('SVG处理失败，请重试');
                        button.textContent = originalText;
                        button.disabled = false;
                        URL.revokeObjectURL(svgUrl);
                    };

                    img.src = svgUrl;
                    return;
                }

                // 添加PDF元数据的函数
                function addPDFMetadata() {
                    // 直接保存，不添加页眉页脚
                    pdf.save(`${fileName}-流程图.pdf`);
                }

                // 添加元数据并保存
                addPDFMetadata();

            } catch (error) {
                console.error('PDF导出失败:', error);
                alert('PDF导出失败: ' + error.message);
                button.textContent = originalText;
                button.disabled = false;
            }
        }

        // 页面初始化
        document.addEventListener('DOMContentLoaded', () => {
            initTabs();
            loadSVG('demand'); // 默认加载第一个 tab
        });
    </script>
</body>
</html>'''
    
    # 生成SVG数据的JavaScript对象
    svg_js_data = "{\n"
    for key, content in svg_data.items():
        svg_js_data += f'            {key}: `{content}`,\n'
    svg_js_data += "        }"
    
    # 替换模板中的占位符
    final_html = html_template.replace('{SVG_DATA_PLACEHOLDER}', svg_js_data)
    
    # 写入文件
    output_file = '人事流程图展示系统-完整单文件版.html'
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(final_html)
        print(f"\n✓ 成功生成: {output_file}")
        print(f"文件大小: {os.path.getsize(output_file) / 1024 / 1024:.2f} MB")
        return True
    except Exception as e:
        print(f"✗ 生成文件失败: {e}")
        return False

if __name__ == "__main__":
    print("开始生成内嵌SVG的HTML文件...")
    print("=" * 50)
    
    success = generate_html()
    
    print("=" * 50)
    if success:
        print("✓ 生成完成！现在您可以直接打开HTML文件使用。")
    else:
        print("✗ 生成失败，请检查错误信息。")
