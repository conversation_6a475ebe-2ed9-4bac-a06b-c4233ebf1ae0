收到
@startuml

title 绩效管理流程

' --- 外观参数设置 (Skinparams) ---
!theme materia
skinparam shadowing false
skinparam defaultFontName "SimSun, NSimSun"
skinparam defaultFontSize 16
skinparam title {
    FontSize 30
    FontColor Black
}
skinparam activity {
    BorderColor #2C3E50
    BackgroundColor #FFFFFF
    ArrowColor #2C3E50
    FontColor #000000
    DiamondBorderColor #D35400
    DiamondBackgroundColor #F1C40F
    DiamondFontColor #000000
}
skinparam note {
    BorderColor #2C3E50
    BackgroundColor #FFFFE0
    FontColor #000000
}
skinparam partition {
    BorderColor #A9A9A9
    BackgroundColor Transparent
    FontColor Blue
    FontSize 16
}
skinparam roundcorner 20

' --- 自动编号变量 ---
!$step = 0
!function $next_step()
  !$step = $step + 1
  !return $step
!endfunction

' --- 泳道定义 ---

|#E6E6FA|人事行政部|
|#FFFACD|员工|
|#E6F3E6|用人部门 (直属经理)|
|#F5F5DC|管理中心|

' --- 流程开始 ---
|管理中心|
start

:$next_step(). 起草/修订\n年度绩效方案;
note right
  <b>输入:</b>
  - 公司年度战略目标
  - 往年绩效数据分析报告
end note
:$next_step(). 审批\n年度绩效方案;

|人事行政部|
:$next_step(). 发布绩效管理制度\n与规则;

    |用人部门 (直属经理)|
    :$next_step(). 向下属传达\n部门/团队目标;
    
    |员工|
    repeat
        :$next_step(). 起草个人\n绩效目标 (初稿);
    |用人部门 (直属经理)|
    backward:$next_step(). 沟通并要求修改;
    repeat while ($next_step(). 审核并确认绩效目标) is (<color:red>驳回</color>) not (<color:green>同意</color>)
    floating note right #FFC0CB
        <font color=red><b>核心痛点:</b></font>
        - 目标设定主观性强
        - 沟通记录多为口头或邮件，
          \n难以追溯和统一管理
    end note
    
    |员工|
    :$next_step(). 签署《绩效目标责任书》;

-><size:40><color:grey>过程跟踪</color></size>;
    |用人部门 (直属经理)|
    :$next_step(). 定期/不定期\n进行绩效辅导;
    note right: 记录关键事件和辅导要点

    |员工|
    :$next_step(). 执行工作任务，\n并记录关键成果;

-><size:40><color:grey>绩效评估</color></size>;
    repeat
    repeat
        |员工|
        :$next_step(). 提交《绩效自评表》\n及相关证明材料;
        
        |用人部门 (直属经理)|
        backward:$next_step(). 说明初评驳回原因;
        repeat while ($next_step() .进行初步评定\n(直线经理初评)) is (<color:red>驳回</color>) not (<color:green>同意</color>)

        if ($next_step(). 是否需要隔级复核?) then (<color:green>Yes</color>)
        else (<color:red>No</color>)
            #palegreen:(A)
            detach
        endif

        |管理中心|
        backward:$next_step(). 说明原因;
        repeat while ($next_step() .进行绩效复核\n与平衡调整) is (<color:red>驳回</color>) not (<color:green>同意</color>)
        note right: 确保部门间\n评价尺度一致
        #palegreen:(A)
        detach
    
    |人事行政部|
    #palegreen:(A)
    :$next_step(). 汇总并核算\n最终绩效等级/分数;
    floating note right #FFC0CB
      <font color=red><b>核心痛点:</b></font>
      1. 评估过程不透明，易引争议。
      2. 大量线下表格统计耗时耗力，
         \n易出错。
      3. 评价标准不统一，结果难公平。
    end note

-><size:40><color:grey>应用执行</color></size>;
    |用人部门 (直属经理)|
    :$next_step(). 进行绩效面谈\n与结果反馈;

    |员工|
    :$next_step(). 确认绩效结果;

    |人事行政部|
    :$next_step(). 将绩效结果归档\n至员工档案;
    :$next_step(). 将绩效结果数据\n传递至薪酬福利模块;
    note right: 作为奖金核算、调薪的依据

end

@enduml