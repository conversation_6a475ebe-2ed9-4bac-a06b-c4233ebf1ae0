@startuml

title 考勤与排班流程

' --- 外观参数设置 (Skinparams) ---
!theme materia
skinparam shadowing false
skinparam defaultFontName "SimSun, NSimSun"
skinparam defaultFontSize 16
skinparam title {
    FontSize 30
    FontColor Black
}
skinparam activity {
    BorderColor #2C3E50
    BackgroundColor #FFFFFF
    ArrowColor #2C3E50
    FontColor #000000
    DiamondBorderColor #D35400
    DiamondBackgroundColor #F1C40F
    DiamondFontColor #000000
}
skinparam note {
    BorderColor #2C3E50
    BackgroundColor #FFFFE0
    FontColor #000000
}
skinparam partition {
    BorderColor #A9A9A9
    BackgroundColor Transparent
    FontColor Blue
    FontSize 16
}
skinparam roundcorner 20

' --- 自动编号变量 ---
!$step = 0
!function $next_step()
  !$step = $step + 1
  !return $step
!endfunction

' --- 流程开始 ---
|#E6F3E6|员工|
|#E6E6FA|用人部门|
|#FFFACD|行政人事部|
|#E0FFFF|IT组|
|#FFF0F5|财务部|

|行政人事部|
start

group 规则制定与排班
-><size:40><color:grey>规则制定与排班</color></size>;
    :$next_step(). 制定/更新考勤与\n假期规则;
    note right
        通常以公司公告或
        邮件形式发布
    end note

    |用人部门|
    :$next_step(). 根据业务需求，\n编制下月排班表;
    note right: 通常使用Excel模板

    :$next_step(). 下发排班表给员工;
end group


group 日常考勤与异常处理
-><size:40><color:grey>日常考勤与异常处理</color></size>;
    |员工|
    :$next_step(). 按排班表上下班，\n在考勤机打卡;

    if ($next_step(). 是否有异常情况?) then (<color:green>Yes</color>)
        :$next_step(). 填写纸质《请假/\n加班申请单》;
        |用人部门|
        :$next_step(). 审批《请假/加班申请单》;
        |行政人事部|
        :$next_step(). 存档已审批的单据;
    else (<color:red>No</color>)
    endif

    |IT组|
    :$next_step(). 定期维护考勤机，\n确保正常运行;
end group


group 数据汇总与核算
-><size:40><color:grey>数据汇总与核算</color></size>;
    |行政人事部|
    :$next_step(). 第二天从考勤机\n导出原始打卡数据;
    note right: 通常为 .dat 或 .xls 文件

    :$next_step(). 手动整理打卡数据，\n并与纸质单据进行核对;
    floating note right #FFC0CB
        <font color=red><b>核心痛点:</b></font>
        1. 数据核对耗时巨大，极易出错。
        2. 假期、加班工时需手动计算。
        3. 规则复杂时，人工核算可靠性低。
    end note

    :$next_step(). 初步生成\n《考勤月报》草稿;

    |用人部门|
    :$next_step(). 接收并审核部门员工的\n《考勤月报》草稿;
    if ($next_step(). 发现数据有误?) then (<color:green>Yes</color>)
        :$next_step(). 与HR沟通，\n进行数据修正;
        |行政人事部|
        :$next_step(). 根据反馈修改数据;
    else (<color:red>No</color>)
    endif
    
    |用人部门|
    #palegreen:$next_step(). 确认本部门考勤结果;

    |行政人事部|
    :$next_step(). 汇总所有部门确认的\n考勤结果，生成最终报表;
end group


group 结果应用
-><size:40><color:grey>结果应用</color></size>;
    |财务部|
    :$next_step(). 接收最终《考勤月报》;
    note right
        用于计算工资中的
        全勤奖、加班费、
        假期扣款等项目
    end note
    :$next_step(). 进行薪资核算;

    |员工|
    :$next_step(). 在工资条中查看\n最终考勤与薪资结果;

    if ($next_step(). 对结果有异议?) then (<color:green>Yes</color>)
        #lightgrey:$next_step(). 向主管或HR发起申诉;
        stop
    else (<color:red>No</color>)
    endif
end group

end
@enduml