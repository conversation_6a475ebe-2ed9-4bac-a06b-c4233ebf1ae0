@startuml

title 人员申请与招聘录用流程

' --- 外观参数设置 (Skinparams) ---
!theme materia
skinparam shadowing false
skinparam defaultFontName "SimSun, NSimSun" 
skinparam defaultFontSize 16
skinparam title {
    FontSize 30
    FontColor Black
}
skinparam activity {
    BorderColor #2C3E50
    BackgroundColor #FFFFFF
    ArrowColor #2C3E50
    FontColor #000000 
    DiamondBorderColor #D35400
    DiamondBackgroundColor #F1C40F
    DiamondFontColor #000000
}
skinparam note {
    BorderColor #2C3E50
    BackgroundColor #FFFFE0
    FontColor #000000
}
skinparam partition {
    BorderColor #A9A9A9
    BackgroundColor Transparent
    FontColor Blue
    FontSize 16
}
skinparam roundcorner 20

' --- 自动编号变量 ---
!$step = 0 
!function $next_step()
  !$step = $step + 1 
  !return $step
!endfunction

' --- 流程开始 (定义所有泳道) ---
|#E6E6FA|行政人事部|
|#E6F3E6|用人部门|
|#F5F5DC|区域总经理|
|#FFAACD|集团总经理|





|用人部门|
start

-><size:40><color:grey>需求与审批</color></size>;
    :$next_step(). 产生用人需求;

    repeat
    repeat
    :$next_step(). 提交《人员需求申请单》;


  |区域总经理|
   repeat while($next_step().区域审批) is (不同意) not (同意) 
    if ($next_step(). 根据岗位级别，\n是否需要集团审批?) then (<color:green>No</color>)
          (A)  
    detach
    else (<color:red>Yes</color>)
    endif

|集团总经理|

    repeat while($next_step().集团审批?) is (不同意) not (同意) 
     (A) 
   detach
    |行政人事部|
    (A) 
    label 开始招聘
        #palegreen:$next_step(). 开始招聘;

        stop
   

@enduml