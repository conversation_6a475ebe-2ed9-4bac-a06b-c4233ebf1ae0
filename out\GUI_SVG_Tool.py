#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GUI SVG展示工具
功能：图形界面选择目录，自动生成SVG展示HTML
"""

import tkinter as tk
from tkinter import filedialog, messagebox, ttk
import os
from pathlib import Path
import threading
import webbrowser
import re

class SVGTool:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🎨 SVG展示工具")
        self.root.geometry("700x600")
        
        # 变量
        self.directory = tk.StringVar()
        self.svg_files = []
        
        self.create_gui()
        
    def create_gui(self):
        """创建GUI界面"""
        # 主框架
        main = ttk.Frame(self.root, padding="20")
        main.pack(fill=tk.BOTH, expand=True)
        
        # 标题
        ttk.Label(main, text="🎨 SVG展示工具", 
                 font=("Microsoft YaHei", 18, "bold")).pack(pady=(0, 10))
        
        ttk.Label(main, text="选择包含SVG文件的目录，自动生成HTML展示系统", 
                 font=("Microsoft YaHei", 10)).pack(pady=(0, 20))
        
        # 目录选择
        dir_frame = ttk.LabelFrame(main, text="📁 选择目录", padding="10")
        dir_frame.pack(fill=tk.X, pady=10)
        
        entry_frame = ttk.Frame(dir_frame)
        entry_frame.pack(fill=tk.X)
        
        self.dir_entry = ttk.Entry(entry_frame, textvariable=self.directory, 
                                  font=("Microsoft YaHei", 10))
        self.dir_entry.pack(side=tk.LEFT, fill=tk.X, expand=True, padx=(0, 10))
        
        ttk.Button(entry_frame, text="浏览", 
                  command=self.browse_folder).pack(side=tk.RIGHT)
        
        # 文件列表
        list_frame = ttk.LabelFrame(main, text="📋 SVG文件", padding="10")
        list_frame.pack(fill=tk.BOTH, expand=True, pady=10)
        
        # 创建列表框
        list_container = ttk.Frame(list_frame)
        list_container.pack(fill=tk.BOTH, expand=True)
        
        self.file_listbox = tk.Listbox(list_container, font=("Microsoft YaHei", 9))
        scrollbar = ttk.Scrollbar(list_container, orient=tk.VERTICAL, 
                                 command=self.file_listbox.yview)
        self.file_listbox.configure(yscrollcommand=scrollbar.set)
        
        self.file_listbox.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # 进度条
        self.progress = ttk.Progressbar(main, mode='determinate')
        self.progress.pack(fill=tk.X, pady=10)
        
        # 状态标签
        self.status = ttk.Label(main, text="请选择包含SVG文件的目录", 
                               font=("Microsoft YaHei", 10))
        self.status.pack(pady=5)
        
        # 按钮
        btn_frame = ttk.Frame(main)
        btn_frame.pack(pady=20)
        
        self.scan_btn = ttk.Button(btn_frame, text="🔍 扫描", 
                                  command=self.scan_files)
        self.scan_btn.pack(side=tk.LEFT, padx=5)
        
        self.generate_btn = ttk.Button(btn_frame, text="🚀 生成HTML", 
                                      command=self.generate, state=tk.DISABLED)
        self.generate_btn.pack(side=tk.LEFT, padx=5)
        
        self.preview_btn = ttk.Button(btn_frame, text="👀 预览", 
                                     command=self.preview, state=tk.DISABLED)
        self.preview_btn.pack(side=tk.LEFT, padx=5)
        
    def browse_folder(self):
        """浏览文件夹"""
        folder = filedialog.askdirectory(title="选择SVG文件目录")
        if folder:
            self.directory.set(folder)
            self.scan_files()
    
    def scan_files(self):
        """扫描SVG文件"""
        folder = self.directory.get()
        if not folder or not os.path.exists(folder):
            messagebox.showerror("错误", "请选择有效目录")
            return
        
        # 清空列表
        self.file_listbox.delete(0, tk.END)
        self.svg_files = []
        
        try:
            # 查找SVG文件
            for file_path in Path(folder).glob("*.svg"):
                if not file_path.name.startswith('.'):
                    self.svg_files.append(file_path)
                    self.file_listbox.insert(tk.END, file_path.name)
            
            # 排序
            self.svg_files.sort(key=lambda x: x.name)
            
            if self.svg_files:
                self.status.config(text=f"发现 {len(self.svg_files)} 个SVG文件")
                self.generate_btn.config(state=tk.NORMAL)
            else:
                self.status.config(text="未发现SVG文件")
                self.generate_btn.config(state=tk.DISABLED)
                
        except Exception as e:
            messagebox.showerror("错误", f"扫描失败: {e}")
    
    def generate(self):
        """生成HTML"""
        if not self.svg_files:
            messagebox.showerror("错误", "没有SVG文件")
            return
        
        threading.Thread(target=self._generate_thread, daemon=True).start()
    
    def _generate_thread(self):
        """生成线程"""
        try:
            self.progress['value'] = 0
            self.root.after(0, lambda: self.status.config(text="读取SVG文件..."))
            
            # 读取SVG
            svg_data = {}
            total = len(self.svg_files)
            
            for i, svg_file in enumerate(self.svg_files):
                try:
                    # 尝试多种编码方式读取文件
                    content = None
                    for encoding in ['utf-8', 'utf-8-sig', 'gbk', 'latin1']:
                        try:
                            with open(svg_file, 'r', encoding=encoding) as f:
                                content = f.read()
                                break
                        except UnicodeDecodeError:
                            continue

                    if content:
                        content = self.clean_svg(content)
                        # 验证SVG内容
                        if '<svg' in content.lower() and '</svg>' in content.lower():
                            # 使用文件名作为键，但转换为安全的JavaScript标识符
                            key = self.safe_js_key(svg_file.stem)
                            svg_data[key] = self.escape_js_string(content)
                            print(f"✓ 成功读取SVG: {svg_file.stem} -> {key} (长度: {len(content)})")
                        else:
                            print(f"✗ 警告: {svg_file} 不是有效的SVG文件")
                            print(f"  内容预览: {content[:100]}...")
                    else:
                        print(f"✗ 无法读取文件: {svg_file}")

                    progress = (i + 1) / total * 50
                    self.progress['value'] = progress

                except Exception as e:
                    print(f"读取 {svg_file} 失败: {e}")
            
            self.root.after(0, lambda: self.status.config(text="生成HTML..."))
            
            # 生成HTML
            html = self.create_html(svg_data)
            
            self.progress['value'] = 75
            
            # 保存文件到原始图片文件夹
            source_dir = Path(self.directory.get())
            output_file = source_dir / "SVG展示.html"
            
            with open(output_file, 'w', encoding='utf-8') as f:
                f.write(html)
            
            self.progress['value'] = 100
            self.root.after(0, lambda: self.status.config(text=f"生成完成: {output_file.name}"))
            self.root.after(0, lambda: self.preview_btn.config(state=tk.NORMAL))
            
           #  self.root.after(0, lambda: messagebox.showinfo("成功", 
              #   f"HTML文件生成成功!\n\n位置: {output_file}\n包含: {len(svg_data)} 个SVG"))
                
        except Exception as e:
            self.root.after(0, lambda: messagebox.showerror("错误", f"生成失败: {e}"))
    
    def clean_svg(self, content):
        """清理SVG内容 - 移除XML声明和DOCTYPE"""
        if not content:
            return ""

        # 移除XML声明（这会导致JavaScript语法错误）
        content = re.sub(r'<\?xml[^>]*\?>\s*', '', content)
        # 移除DOCTYPE声明
        content = re.sub(r'<!DOCTYPE[^>]*>\s*', '', content)
        # 移除注释（但保留SVG内容）
        content = re.sub(r'<!--.*?-->', '', content, flags=re.DOTALL)
        # 清理多余空白但保持SVG结构
        content = re.sub(r'\s+', ' ', content)
        content = content.strip()
        return content

    def escape_js_string(self, content):
        """转义JavaScript字符串中的特殊字符"""
        if not content:
            return ""

        # 替换反斜杠、引号和模板字符串特殊字符
        content = content.replace('\\', '\\\\')
        content = content.replace('`', '\\`')
        content = content.replace('${', '\\${')
        # 替换换行符为空格
        content = content.replace('\n', ' ')
        content = content.replace('\r', ' ')
        # 移除多余的空格
        content = re.sub(r'\s+', ' ', content)
        return content.strip()
    
    def clean_name(self, filename):
        """清理文件名"""
        name = Path(filename).stem
        name = name.replace('_', ' ').replace('-', ' ')
        return ' '.join(word.capitalize() for word in name.split())

    def safe_js_key(self, filename):
        """将文件名转换为安全的JavaScript对象键名"""
        # 移除文件扩展名
        name = Path(filename).stem
        # 将中文和特殊字符转换为拼音或英文缩写
        key_mapping = {
            '培训管理流程-活动图': 'training',
            '培训管理': 'training',
            '绩效管理流程-活动图': 'performance',
            '绩效管理': 'performance',
            '考勤与排班流程-活动图': 'attendance',
            '考勤与排班': 'attendance',
            '人员需求申请流程-活动图': 'demand',
            '人员需求申请': 'demand',
            '招聘与录用流程-活动图': 'recruitment',
            '招聘与录用': 'recruitment',
            '薪酬福利管理流程-活动图': 'salary',
            '薪酬福利管理': 'salary',
            '员工职位调动及调薪-活动图': 'transfer',
            '员工职位调动及调薪': 'transfer',
            '员工辞职或解雇流程-活动图': 'resignation',
            '员工辞职或解雇': 'resignation'
        }

        # 如果有映射，使用映射的值
        if name in key_mapping:
            return key_mapping[name]

        # 否则生成安全的键名
        import hashlib
        # 使用文件名的前几个字符加上哈希值
        safe_name = re.sub(r'[^a-zA-Z0-9]', '', name)
        if not safe_name:
            safe_name = 'file'

        # 确保键名以字母开头（JavaScript要求）
        if safe_name and safe_name[0].isdigit():
            safe_name = 'file' + safe_name

        hash_suffix = hashlib.md5(name.encode('utf-8')).hexdigest()[:6]
        return f"{safe_name}_{hash_suffix}"
    
    def create_html(self, svg_data):
        """创建HTML"""
        # 生成Tab - 需要保存原始文件名用于显示
        tabs = []
        contents = []
        key_to_name = {}  # 保存键名到显示名称的映射

        for i, key in enumerate(svg_data.keys()):
            # 从原始文件名获取显示名称
            original_name = None
            for svg_file in self.svg_files:
                if self.safe_js_key(svg_file.stem) == key:
                    original_name = self.clean_name(svg_file.stem)
                    break

            name = original_name or self.clean_name(key)
            key_to_name[key] = name
            active = " active" if i == 0 else ""

            tabs.append(f'                <button class="tab-button{active}" data-tab="{key}">{name}</button>')
            contents.append(f'''            <div class="tab-content{active}" id="{key}">
                <div class="svg-container" id="{key}-svg"></div>
            </div>''')

        # 生成SVG数据 - 使用与原版相同的格式
        svg_js = []
        for key, escaped_svg in svg_data.items():
            # SVG内容已经在读取时进行了转义处理
            # 使用无引号的键名格式（与原版保持一致），并添加逗号
            svg_js.append(f'            {key}: `{escaped_svg}`,')
        
        first_key = list(svg_data.keys())[0] if svg_data else 'default'
        
        return f'''<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SVG展示</title>
    <style>
        * {{ margin: 0; padding: 0; box-sizing: border-box; }}
        body {{ font-family: 'Microsoft YaHei', Arial, sans-serif; background: #f5f5f5; height: 100vh; overflow: hidden; }}
        .container {{ width: 100%; height: 100vh; background: white; display: flex; flex-direction: column; }}
        .tab-container {{ background: #f8f9fa; border-bottom: 1px solid #dee2e6; flex-shrink: 0; display: flex; align-items: center; }}
        .tab-nav {{ display: flex; overflow-x: auto; padding: 0 20px; flex: 1; }}
        .global-controls {{ padding: 0 20px; display: flex; align-items: center; gap: 15px; border-left: 1px solid #dee2e6; }}
        .tab-button {{ background: none; border: none; padding: 15px 20px; cursor: pointer; font-size: 14px; font-weight: 500; color: #6c757d; border-bottom: 3px solid transparent; transition: all 0.3s ease; white-space: nowrap; min-width: 120px; }}
        .tab-button:hover {{ color: #495057; background: rgba(0,0,0,0.05); }}
        .tab-button.active {{ color: #007bff; border-bottom-color: #007bff; background: white; }}
        .content-area {{ flex: 1; display: flex; flex-direction: column; overflow: hidden; }}
        .tab-content {{ display: none; animation: fadeIn 0.3s ease-in; flex: 1; overflow: hidden; }}
        .tab-content.active {{ display: flex; flex-direction: column; }}
        @keyframes fadeIn {{ from {{ opacity: 0; transform: translateY(10px); }} to {{ opacity: 1; transform: translateY(0); }} }}
        .scale-control, .quality-control {{ display: flex; align-items: center; gap: 5px; font-size: 12px; color: #666; }}
        .scale-slider {{ width: 80px; height: 4px; background: #ddd; border-radius: 2px; outline: none; -webkit-appearance: none; appearance: none; }}
        .scale-slider::-webkit-slider-thumb {{ -webkit-appearance: none; width: 16px; height: 16px; background: #007bff; border-radius: 50%; cursor: pointer; }}
        .scale-slider::-moz-range-thumb {{ width: 16px; height: 16px; background: #007bff; border-radius: 50%; cursor: pointer; border: none; }}
        .quality-input {{ width: 50px; padding: 4px 6px; border: 1px solid #ddd; border-radius: 4px; font-size: 12px; text-align: center; }}
        #scale-value {{ min-width: 35px; font-weight: 500; color: #007bff; }}
        .export-btn {{ background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border: none; padding: 12px 24px; border-radius: 25px; cursor: pointer; font-size: 14px; font-weight: 500; transition: all 0.3s ease; box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4); }}
        .export-btn:hover {{ transform: translateY(-2px); box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6); }}
        .export-btn:disabled {{ opacity: 0.6; cursor: not-allowed; transform: none; }}
        .svg-container {{ flex: 1; background: white; overflow: auto; padding: 20px; }}
        .svg-container svg {{ display: block; margin: 0 auto; border: 1px solid #e9ecef; border-radius: 8px; }}
        .error {{ color: #dc3545; text-align: center; padding: 20px; background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 8px; margin: 20px; }}
    </style>
</head>
<body>
    <div class="container">
        <div class="tab-container">
            <div class="tab-nav">
{chr(10).join(tabs)}
            </div>
            <div class="global-controls">
                <div class="scale-control">
                    <label>显示比例:</label>
                    <input type="range" class="scale-slider" value="1" min="0.2" max="1" step="0.05" id="svg-scale">
                    <span id="scale-value">100%</span>
                </div>
                <div class="quality-control">
                    <label>导出清晰度:</label>
                    <input type="number" class="quality-input" value="2" min="1" max="5" step="1" id="global-quality">
                    <span>倍</span>
                </div>
                <button class="export-btn" onclick="exportCurrentTab()" id="global-export-btn">📄 导出PDF</button>
            </div>
        </div>
        <div class="content-area">
{chr(10).join(contents)}
        </div>
    </div>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://unpkg.com/canvg/lib/umd.js"></script>
    <script>
        const svgData = {{
{chr(10).join(svg_js)}
        }};
        let currentActiveTab = '{first_key}';

        function initTabs() {{
            const tabButtons = document.querySelectorAll('.tab-button');
            const tabContents = document.querySelectorAll('.tab-content');
            tabButtons.forEach(button => {{
                button.addEventListener('click', () => {{
                    const targetTab = button.getAttribute('data-tab');
                    tabButtons.forEach(btn => btn.classList.remove('active'));
                    tabContents.forEach(content => content.classList.remove('active'));
                    button.classList.add('active');
                    document.getElementById(targetTab).classList.add('active');
                    currentActiveTab = targetTab;
                    loadSVG(targetTab);
                }});
            }});
        }}

        function loadSVG(tabName) {{
            const container = document.getElementById(`${{tabName}}-svg`);

            if (svgData[tabName] && svgData[tabName].trim()) {{
                container.innerHTML = svgData[tabName];
                const svgElement = container.querySelector('svg');

                if (svgElement) {{
                    // 应用当前缩放比例
                    const scaleSlider = document.getElementById('svg-scale');
                    if (scaleSlider) {{
                        const scale = parseFloat(scaleSlider.value);
                        svgElement.style.transform = `scale(${{scale}})`;
                        svgElement.style.transformOrigin = 'center center';
                    }}
                }}
            }} else {{
                container.innerHTML = `
                    <div class="error">
                        <h3>SVG 内容未找到</h3>
                        <p>流程图 "${{tabName}}" 的内容暂时无法显示</p>
                    </div>
                `;
            }}
        }}

        function exportCurrentTab() {{
            // 获取当前活动Tab的显示名称
            const activeTabButton = document.querySelector('.tab-button.active');
            const fileName = activeTabButton ? activeTabButton.textContent.trim() : (currentActiveTab || '图片');
            exportToPDF(currentActiveTab, fileName);
        }}

        // SVG缩放功能
        function initSVGScale() {{
            const scaleSlider = document.getElementById('svg-scale');
            const scaleValue = document.getElementById('scale-value');

            if (scaleSlider && scaleValue) {{
                scaleSlider.addEventListener('input', function() {{
                    const scale = parseFloat(this.value);
                    scaleValue.textContent = Math.round(scale * 100) + '%';

                    // 应用缩放到当前活动的SVG
                    const currentSvgContainer = document.getElementById(`${{currentActiveTab}}-svg`);
                    const currentSvg = currentSvgContainer ? currentSvgContainer.querySelector('svg') : null;

                    if (currentSvg) {{
                        currentSvg.style.transform = `scale(${{scale}})`;
                        currentSvg.style.transformOrigin = 'center center';
                    }}
                }});

                // 初始化显示
                scaleValue.textContent = Math.round(parseFloat(scaleSlider.value) * 100) + '%';
            }}
        }}

        // 应用缩放到新加载的SVG
        function applySVGScale(svgElement) {{
            const scaleSlider = document.getElementById('svg-scale');
            if (scaleSlider && svgElement) {{
                const scale = parseFloat(scaleSlider.value);
                svgElement.style.transform = `scale(${{scale}})`;
                svgElement.style.transformOrigin = 'center center';
            }}
        }}

        // PDF导出功能
        async function exportToPDF(tabName, fileName) {{
            const button = document.getElementById('global-export-btn');
            const originalText = button.textContent;

            try {{
                button.textContent = '导出中...';
                button.disabled = true;

                const svgContainer = document.getElementById(`${{tabName}}-svg`);
                const svgElement = svgContainer.querySelector('svg');

                if (!svgElement) {{
                    throw new Error('未找到可导出的图形内容');
                }}

                // 检查必要的库是否加载
                if (typeof window.jspdf === 'undefined') {{
                    throw new Error('jsPDF 库未加载，请检查网络连接');
                }}

                // 获取SVG的尺寸信息
                let svgWidth, svgHeight;

                // 尝试从不同属性获取SVG尺寸
                if (svgElement.viewBox && svgElement.viewBox.baseVal) {{
                    svgWidth = svgElement.viewBox.baseVal.width;
                    svgHeight = svgElement.viewBox.baseVal.height;
                }} else if (svgElement.getAttribute('width') && svgElement.getAttribute('height')) {{
                    svgWidth = parseFloat(svgElement.getAttribute('width').replace(/[^\\d.]/g, ''));
                    svgHeight = parseFloat(svgElement.getAttribute('height').replace(/[^\\d.]/g, ''));
                }} else {{
                    // 使用实际渲染尺寸
                    const rect = svgElement.getBoundingClientRect();
                    svgWidth = rect.width || 800;
                    svgHeight = rect.height || 600;
                }}

                // 创建PDF，根据SVG尺寸选择页面方向和格式
                const {{ jsPDF }} = window.jspdf;
                const isLandscape = svgWidth > svgHeight;

                // 根据SVG尺寸选择合适的页面格式
                let format = 'a4';
                if (svgWidth > 1200 || svgHeight > 1200) {{
                    format = 'a3';
                }} else if (svgWidth > 2000 || svgHeight > 2000) {{
                    format = 'a2';
                }}

                const pdf = new jsPDF({{
                    orientation: isLandscape ? 'landscape' : 'portrait',
                    unit: 'pt',
                    format: format
                }});

                // 获取PDF页面尺寸
                const pageWidth = pdf.internal.pageSize.getWidth();
                const pageHeight = pdf.internal.pageSize.getHeight();
                const margin = 20; // 边距

                // 计算缩放比例，保持宽高比
                const availableWidth = pageWidth - (margin * 2);
                const availableHeight = pageHeight - (margin * 2);
                const scaleX = availableWidth / svgWidth;
                const scaleY = availableHeight / svgHeight;
                const scale = Math.min(scaleX, scaleY, 1); // 不放大，只缩小

                const scaledWidth = svgWidth * scale;
                const scaledHeight = svgHeight * scale;

                // 居中位置
                const x = (pageWidth - scaledWidth) / 2;
                const y = (pageHeight - scaledHeight) / 2;

                // 获取全局设置的像素密度
                const globalQualityInput = document.getElementById('global-quality');
                const pixelRatio = globalQualityInput ? parseInt(globalQualityInput.value) || 2 : 2;

                // 使用canvg将SVG转换为高质量Canvas
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');

                // 设置用户指定分辨率的Canvas
                canvas.width = scaledWidth * pixelRatio;
                canvas.height = scaledHeight * pixelRatio;

                // 获取SVG字符串
                const svgString = new XMLSerializer().serializeToString(svgElement);

                // 使用canvg渲染SVG到Canvas
                if (typeof canvg !== 'undefined') {{
                    // 使用canvg库进行高质量渲染
                    const v = canvg.Canvg.fromString(ctx, svgString, {{
                        ignoreDimensions: true,
                        scaleWidth: canvas.width,
                        scaleHeight: canvas.height,
                        enableRedraw: false
                    }});

                    await v.start();

                    // 转换为高质量图片数据
                    const imgData = canvas.toDataURL('image/png', 1.0);

                    // 添加到PDF - 使用PDF页面上的缩放尺寸
                    pdf.addImage(imgData, 'PNG', x, y, scaledWidth, scaledHeight);

                    // 保存PDF
                    pdf.save(`${{fileName}}-流程图.pdf`);

                    // 成功提示
                    button.textContent = '✅ 导出成功';
                    setTimeout(() => {{
                        button.textContent = originalText;
                        button.disabled = false;
                    }}, 2000);

                }} else {{
                    // 备用方案：使用传统方法
                    console.warn('canvg库未加载，使用备用方案');

                    // 缩放上下文
                    ctx.scale(pixelRatio, pixelRatio);
                    ctx.fillStyle = 'white';
                    ctx.fillRect(0, 0, scaledWidth, scaledHeight);

                    // 创建Image对象加载SVG
                    const img = new Image();
                    const svgBlob = new Blob([svgString], {{ type: 'image/svg+xml;charset=utf-8' }});
                    const svgUrl = URL.createObjectURL(svgBlob);

                    img.onload = function() {{
                        ctx.drawImage(img, 0, 0, scaledWidth, scaledHeight);
                        const imgData = canvas.toDataURL('image/png', 1.0);
                        pdf.addImage(imgData, 'PNG', x, y, scaledWidth, scaledHeight);

                        // 保存PDF
                        pdf.save(`${{fileName}}-流程图.pdf`);

                        // 成功提示
                        button.textContent = '✅ 导出成功';
                        setTimeout(() => {{
                            button.textContent = originalText;
                            button.disabled = false;
                        }}, 2000);

                        URL.revokeObjectURL(svgUrl);
                    }};

                    img.onerror = function() {{
                        alert('SVG处理失败，请重试');
                        button.textContent = originalText;
                        button.disabled = false;
                        URL.revokeObjectURL(svgUrl);
                    }};

                    img.src = svgUrl;
                    return;
                }}



            }} catch (error) {{
                console.error('PDF导出失败:', error);
                alert('PDF导出失败: ' + error.message);
                button.textContent = originalText;
                button.disabled = false;
            }}
        }}

        // 页面初始化
        document.addEventListener('DOMContentLoaded', () => {{
            initTabs();
            initSVGScale();
            loadSVG(currentActiveTab);
        }});




    </script>
</body>
</html>'''
        
    def preview(self):
        """预览HTML"""
        output_dir = Path(self.directory.get())
        output_file = output_dir / "SVG展示.html"
        
        if output_file.exists():
            webbrowser.open(f"file://{output_file.absolute()}")
        else:
            messagebox.showerror("错误", "HTML文件不存在")
    
    def run(self):
        """运行程序"""
        self.root.mainloop()

if __name__ == "__main__":
    app = SVGTool()
    app.run()
