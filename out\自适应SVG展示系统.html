<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>自适应SVG展示系统</title>
    <style>
        * { margin: 0; padding: 0; box-sizing: border-box; }
        body { font-family: 'Microsoft YaHei', Arial, sans-serif; background: #f5f5f5; height: 100vh; overflow: hidden; }
        .container { width: 100%; height: 100vh; background: white; display: flex; flex-direction: column; }
        .tab-container { background: #f8f9fa; border-bottom: 1px solid #dee2e6; flex-shrink: 0; display: flex; align-items: center; }
        .tab-nav { display: flex; overflow-x: auto; padding: 0 20px; flex: 1; }
        .global-controls { padding: 0 20px; display: flex; align-items: center; gap: 15px; border-left: 1px solid #dee2e6; }
        .tab-button { background: none; border: none; padding: 15px 20px; cursor: pointer; font-size: 14px; font-weight: 500; color: #6c757d; border-bottom: 3px solid transparent; transition: all 0.3s ease; white-space: nowrap; min-width: 120px; }
        .tab-button:hover { color: #495057; background: rgba(0,0,0,0.05); }
        .tab-button.active { color: #007bff; border-bottom-color: #007bff; background: white; }
        .content-area { flex: 1; display: flex; flex-direction: column; overflow: hidden; }
        .tab-content { display: none; animation: fadeIn 0.3s ease-in; flex: 1; overflow: hidden; }
        .tab-content.active { display: flex; flex-direction: column; }
        @keyframes fadeIn { from { opacity: 0; transform: translateY(10px); } to { opacity: 1; transform: translateY(0); } }
        .scale-control, .quality-control { display: flex; align-items: center; gap: 5px; font-size: 12px; color: #666; }
        .scale-slider { width: 80px; height: 4px; background: #ddd; border-radius: 2px; outline: none; -webkit-appearance: none; appearance: none; }
        .scale-slider::-webkit-slider-thumb { -webkit-appearance: none; width: 16px; height: 16px; background: #007bff; border-radius: 50%; cursor: pointer; }
        .scale-slider::-moz-range-thumb { width: 16px; height: 16px; background: #007bff; border-radius: 50%; cursor: pointer; border: none; }
        .quality-input { width: 50px; padding: 4px 6px; border: 1px solid #ddd; border-radius: 4px; font-size: 12px; text-align: center; }
        #scale-value { min-width: 35px; font-weight: 500; color: #007bff; }
        .export-btn { background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); color: white; border: none; padding: 12px 24px; border-radius: 25px; cursor: pointer; font-size: 14px; font-weight: 500; transition: all 0.3s ease; box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4); }
        .export-btn:hover { transform: translateY(-2px); box-shadow: 0 6px 20px rgba(102, 126, 234, 0.6); }
        .export-btn:disabled { opacity: 0.6; cursor: not-allowed; transform: none; }
        .svg-container { flex: 1; background: white; overflow: auto; padding: 20px; }
        .svg-container svg { display: block; margin: 0 auto; border: 1px solid #e9ecef; border-radius: 8px; }
        .error { color: #dc3545; text-align: center; padding: 20px; background: #f8d7da; border: 1px solid #f5c6cb; border-radius: 8px; margin: 20px; }
    </style>
</head>
<body>
    <div class="container">
        <div class="tab-container">
            <div class="tab-nav">
                <button class="tab-button active" data-tab="file1_8a86c4">1绩效管理</button>
                <button class="tab-button" data-tab="file2_9bb592">2考勤与排班</button>
                <button class="tab-button" data-tab="file3_b6b365">3培训管理</button>
            </div>
            <div class="global-controls">
                <div class="scale-control">
                    <label>显示比例:</label>
                    <input type="range" class="scale-slider" value="1" min="0.2" max="1" step="0.05" id="svg-scale">
                    <span id="scale-value">100%</span>
                </div>
                <div class="quality-control">
                    <label>导出清晰度:</label>
                    <input type="number" class="quality-input" value="2" min="1" max="5" step="1" id="global-quality">
                    <span>倍</span>
                </div>
                <button class="export-btn" onclick="exportCurrentTab()" id="global-export-btn">📄 导出PDF</button>
            </div>
        </div>
        <div class="content-area">
            <div class="tab-content active" id="file1_8a86c4">
                <div class="svg-container" id="file1_8a86c4-svg"></div>
            </div>
            <div class="tab-content" id="file2_9bb592">
                <div class="svg-container" id="file2_9bb592-svg"></div>
            </div>
            <div class="tab-content" id="file3_b6b365">
                <div class="svg-container" id="file3_b6b365-svg"></div>
            </div>
        </div>
    </div>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jspdf/2.5.1/jspdf.umd.min.js"></script>
    <script src="https://unpkg.com/canvg/lib/umd.js"></script>
    <script>
        const svgData = {
            file1_8a86c4: `<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" contentStyleType="text/css" height="2503.125px" preserveAspectRatio="none" style="width:1898px;height:2503px;" version="1.1" viewBox="0 0 1898 2503" width="1898.9583px" zoomAndPan="magnify"><defs><linearGradient id="g1ep9m9cn6yzd10" x1="50%" x2="50%" y1="0%" y2="100%"><stop offset="0%" stop-color="#FFFFFF"/><stop offset="100%" stop-color="#FFFFFF"/></linearGradient></defs><g><rect fill="url(#g1ep9m9cn6yzd10)" height="72.64" id="_title" rx="10.4167" ry="10.4167" style="stroke:#CCCCCC;stroke-width:1.0416666666666667;" width="226.6927" x="834.5703" y="20.8333"/><text fill="#000000" font-family="sans-serif" font-size="31.25" font-weight="bold" lengthAdjust="spacing" textLength="193.3594" x="851.237" y="69.9402">&#32489;&#25928;&#31649;&#29702;&#27969;&#31243;</text><rect fill="url(#g1ep9m9cn6yzd10)" height="41.7806" style="stroke:url(#g1ep9m9cn6yzd10);stroke-width:1.0416666666666667;" width="1864.5833" x="15.625" y="105.9733"/><rect fill="#F5F5DC" height="2385.1563" style="stroke:#F5F5DC;stroke-width:1.0416666666666667;" width="442.7083" x="15.625" y="105.9733"/><ellipse cx="114.5833" cy="163.3789" fill="#9C27B0" rx="10.4167" ry="10.4167" style="stroke:#1A78C2;stroke-width:1.0416666666666667;"/><path d="M202.0833,185.1888 L202.0833,228.0924 L181.25,232.2591 L202.0833,236.4258 L202.0833,279.3294 A0,0 0 0 0 202.0833,279.3294 L420.8333,279.3294 A0,0 0 0 0 420.8333,279.3294 L420.8333,195.6055 L410.4167,185.1888 L202.0833,185.1888 A0,0 0 0 0 202.0833,185.1888 " fill="#FFFFE0" style="stroke:#2C3E50;stroke-width:1.0416666666666667;"/><path d="M410.4167,185.1888 L410.4167,195.6055 L420.8333,195.6055 L410.4167,185.1888 " fill="#FFFFE0" style="stroke:#2C3E50;stroke-width:1.0416666666666667;"/><text fill="#000000" font-family="sans-serif" font-size="16.6667" font-weight="bold" lengthAdjust="spacing" textLength="40.625" x="218.75" y="218.1152">&#36755;&#20837;:</text><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="142.7083" x="218.75" y="239.0788">- &#20844;&#21496;&#24180;&#24230;&#25112;&#30053;&#30446;&#26631;</text><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="176.0417" x="218.75" y="260.0423">- &#24448;&#24180;&#32489;&#25928;&#25968;&#25454;&#20998;&#26512;&#25253;&#21578;</text><rect fill="#FFFFFF" height="75.2604" rx="10.4167" ry="10.4167" style="stroke:#2C3E50;stroke-width:1.0416666666666667;" width="133.3333" x="47.9167" y="194.6289"/><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="88.5417" x="64.5833" y="228.597">1. &#36215;&#33609;/&#20462;&#35746;</text><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="100" x="64.5833" y="249.5605">&#24180;&#24230;&#32489;&#25928;&#26041;&#26696;</text><rect fill="#FFFFFF" height="75.2604" rx="10.4167" ry="10.4167" style="stroke:#2C3E50;stroke-width:1.0416666666666667;" width="133.3333" x="47.9167" y="300.1628"/><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="51.0417" x="64.5833" y="334.1309">2. &#23457;&#25209;</text><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="100" x="64.5833" y="355.0944">&#24180;&#24230;&#32489;&#25928;&#26041;&#26696;</text><rect fill="none" height="626.8392" rx="10.4167" ry="10.4167" style="stroke:#A9A9A9;stroke-width:1.0416666666666667;" width="432.2917" x="21.875" y="1371.2321"/><path d="M119.7917,1371.2321 L119.7917,1405.7373 L109.375,1416.154 L21.875,1416.154 " fill="none" style="stroke:#A9A9A9;stroke-width:1.0416666666666667;"/><text fill="#0000FF" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="66.6667" x="35.4167" y="1399.9919">&#32489;&#25928;&#35780;&#20272;</text><path d="M215.625,1721.1833 L215.625,1743.1234 L194.7917,1747.29 L215.625,1751.4567 L215.625,1773.3968 A0,0 0 0 0 215.625,1773.3968 L441.6667,1773.3968 A0,0 0 0 0 441.6667,1773.3968 L441.6667,1731.5999 L431.25,1721.1833 L215.625,1721.1833 A0,0 0 0 0 215.625,1721.1833 " fill="#FFFFE0" style="stroke:#2C3E50;stroke-width:1.0416666666666667;"/><path d="M431.25,1721.1833 L431.25,1731.5999 L441.6667,1731.5999 L431.25,1721.1833 " fill="#FFFFE0" style="stroke:#2C3E50;stroke-width:1.0416666666666667;"/><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="183.3333" x="232.2917" y="1754.1097">&#30830;&#20445;&#37096;&#38376;&#38388;&#35780;&#20215;&#23610;&#24230;&#19968;&#33268;</text><rect fill="#FFFFFF" height="75.2604" rx="10.4167" ry="10.4167" style="stroke:#2C3E50;stroke-width:1.0416666666666667;" width="160.4167" x="34.375" y="1709.6598"/><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="127.0833" x="51.0417" y="1743.6279">13. &#36827;&#34892;&#32489;&#25928;&#22797;&#26680;</text><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="83.3333" x="51.0417" y="1764.5915">&#19982;&#24179;&#34913;&#35843;&#25972;</text><line style="stroke:#9C27B0;stroke-width:2.0833333333333335;" x1="15.625" x2="15.625" y1="105.9733" y2="2491.1296"/><rect fill="#E6E6FA" height="2385.1563" style="stroke:#E6E6FA;stroke-width:1.0416666666666667;" width="535.4167" x="458.3333" y="105.9733"/><rect fill="#FFFFFF" height="75.2604" rx="10.4167" ry="10.4167" style="stroke:#2C3E50;stroke-width:1.0416666666666667;" width="184.375" x="486.9792" y="396.2565"/><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="151.0417" x="503.6458" y="430.2246">3. &#21457;&#24067;&#32489;&#25928;&#31649;&#29702;&#21046;&#24230;</text><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="50" x="503.6458" y="451.1882">&#19982;&#35268;&#21017;</text><rect fill="none" height="626.8392" rx="10.4167" ry="10.4167" style="stroke:#A9A9A9;stroke-width:1.0416666666666667;" width="525" x="464.5833" y="1371.2321"/><path d="M562.5,1371.2321 L562.5,1405.7373 L552.0833,1416.154 L464.5833,1416.154 " fill="none" style="stroke:#A9A9A9;stroke-width:1.0416666666666667;"/><text fill="#0000FF" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="66.6667" x="478.125" y="1399.9919">&#32489;&#25928;&#35780;&#20272;</text><path d="M685.4167,1841.1702 L685.4167,1977.238 L979.1667,1977.238 L979.1667,1851.5869 L968.75,1841.1702 L685.4167,1841.1702 " fill="#FFC0CB" style="stroke:#2C3E50;stroke-width:1.0416666666666667;"/><path d="M968.75,1841.1702 L968.75,1851.5869 L979.1667,1851.5869 L968.75,1841.1702 " fill="#FFC0CB" style="stroke:#2C3E50;stroke-width:1.0416666666666667;"/><text fill="#FF0000" font-family="sans-serif" font-size="16.6667" font-weight="bold" lengthAdjust="spacing" textLength="75" x="702.0833" y="1874.0967">&#26680;&#24515;&#30171;&#28857;:</text><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="234.375" x="702.0833" y="1895.0602">1. &#35780;&#20272;&#36807;&#31243;&#19981;&#36879;&#26126;&#65292;&#26131;&#24341;&#20105;&#35758;&#12290;</text><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="234.375" x="702.0833" y="1916.0238">2. &#22823;&#37327;&#32447;&#19979;&#34920;&#26684;&#32479;&#35745;&#32791;&#26102;&#32791;&#21147;&#65292;</text><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="66.6667" x="714.5833" y="1936.9873">&#26131;&#20986;&#38169;&#12290;</text><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="251.0417" x="702.0833" y="1957.9508">3. &#35780;&#20215;&#26631;&#20934;&#19981;&#32479;&#19968;&#65292;&#32467;&#26524;&#38590;&#20844;&#24179;&#12290;</text><rect fill="#FFFFFF" height="75.2604" rx="10.4167" ry="10.4167" style="stroke:#2C3E50;stroke-width:1.0416666666666667;" width="170.8333" x="493.75" y="1871.5739"/><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="110.4167" x="510.4167" y="1905.542">14. &#27719;&#24635;&#24182;&#26680;&#31639;</text><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="137.5" x="510.4167" y="1926.5055">&#26368;&#32456;&#32489;&#25928;&#31561;&#32423;/&#20998;&#25968;</text><rect fill="none" height="417.7083" rx="10.4167" ry="10.4167" style="stroke:#A9A9A9;stroke-width:1.0416666666666667;" width="467.7083" x="477.0833" y="2031.7546"/><path d="M575,2031.7546 L575,2066.2598 L564.5833,2076.6764 L477.0833,2076.6764 " fill="none" style="stroke:#A9A9A9;stroke-width:1.0416666666666667;"/><text fill="#0000FF" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="66.6667" x="490.625" y="2060.5143">&#24212;&#29992;&#25191;&#34892;</text><rect fill="#FFFFFF" height="75.2604" rx="10.4167" ry="10.4167" style="stroke:#2C3E50;stroke-width:1.0416666666666667;" width="177.0833" x="490.625" y="2265.6087"/><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="143.75" x="507.2917" y="2299.5768">17. &#23558;&#32489;&#25928;&#32467;&#26524;&#24402;&#26723;</text><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="83.3333" x="507.2917" y="2320.5404">&#33267;&#21592;&#24037;&#26723;&#26696;</text><path d="M691.6667,2373.2259 L691.6667,2395.166 L670.8333,2399.3327 L691.6667,2403.4993 L691.6667,2425.4395 A0,0 0 0 0 691.6667,2425.4395 L934.375,2425.4395 A0,0 0 0 0 934.375,2425.4395 L934.375,2383.6426 L923.9583,2373.2259 L691.6667,2373.2259 A0,0 0 0 0 691.6667,2373.2259 " fill="#FFFFE0" style="stroke:#2C3E50;stroke-width:1.0416666666666667;"/><path d="M923.9583,2373.2259 L923.9583,2383.6426 L934.375,2383.6426 L923.9583,2373.2259 " fill="#FFFFE0" style="stroke:#2C3E50;stroke-width:1.0416666666666667;"/><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="200" x="708.3333" y="2406.1523">&#20316;&#20026;&#22870;&#37329;&#26680;&#31639;&#12289;&#35843;&#34218;&#30340;&#20381;&#25454;</text><rect fill="#FFFFFF" height="75.2604" rx="10.4167" ry="10.4167" style="stroke:#2C3E50;stroke-width:1.0416666666666667;" width="183.3333" x="487.5" y="2361.7025"/><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="143.75" x="504.1667" y="2395.6706">18. &#23558;&#32489;&#25928;&#32467;&#26524;&#25968;&#25454;</text><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="150" x="504.1667" y="2416.6341">&#20256;&#36882;&#33267;&#34218;&#37228;&#31119;&#21033;&#27169;&#22359;</text><ellipse cx="579.1667" cy="2480.7129" fill="none" rx="10.4167" ry="10.4167" style="stroke:#9C27B0;stroke-width:1.5625;"/><line style="stroke:#9C27B0;stroke-width:2.604166666666667;" x1="572.7217" x2="585.6117" y1="2474.2679" y2="2487.1579"/><line style="stroke:#9C27B0;stroke-width:2.604166666666667;" x1="585.6117" x2="572.7217" y1="2474.2679" y2="2487.1579"/><line style="stroke:#9C27B0;stroke-width:2.0833333333333335;" x1="458.3333" x2="458.3333" y1="105.9733" y2="2491.1296"/><rect fill="#FFFACD" height="2385.1563" style="stroke:#FFFACD;stroke-width:1.0416666666666667;" width="357.8125" x="993.75" y="105.9733"/><rect fill="none" height="451.3916" rx="10.4167" ry="10.4167" style="stroke:#A9A9A9;stroke-width:1.0416666666666667;" width="334.8958" x="1000" y="543.6279"/><path d="M1097.9167,543.6279 L1097.9167,578.1331 L1087.5,588.5498 L1000,588.5498 " fill="none" style="stroke:#A9A9A9;stroke-width:1.0416666666666667;"/><text fill="#0000FF" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="66.6667" x="1013.5417" y="572.3877">&#21046;&#23450;&#30446;&#26631;</text><rect fill="#FFFFFF" height="75.2604" rx="10.4167" ry="10.4167" style="stroke:#2C3E50;stroke-width:1.0416666666666667;" width="147.9167" x="1053.6458" y="702.3519"/><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="84.375" x="1070.3125" y="736.32">5. &#36215;&#33609;&#20010;&#20154;</text><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="114.5833" x="1070.3125" y="757.2835">&#32489;&#25928;&#30446;&#26631; (&#21021;&#31295;)</text><rect fill="#FFFFFF" height="54.2969" rx="10.4167" ry="10.4167" style="stroke:#2C3E50;stroke-width:1.0416666666666667;" width="234.375" x="1010.4167" y="923.9665"/><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="201.0417" x="1027.0833" y="957.9346">7. &#31614;&#32626;&#12298;&#32489;&#25928;&#30446;&#26631;&#36131;&#20219;&#20070;&#12299;</text><rect fill="none" height="246.4844" rx="10.4167" ry="10.4167" style="stroke:#A9A9A9;stroke-width:1.0416666666666667;" width="288.0208" x="1033.3333" y="1051.5055"/><path d="M1131.25,1051.5055 L1131.25,1086.0107 L1120.8333,1096.4274 L1033.3333,1096.4274 " fill="none" style="stroke:#A9A9A9;stroke-width:1.0416666666666667;"/><text fill="#0000FF" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="66.6667" x="1046.875" y="1080.2653">&#36807;&#31243;&#36319;&#36394;</text><rect fill="#FFFFFF" height="75.2604" rx="10.4167" ry="10.4167" style="stroke:#2C3E50;stroke-width:1.0416666666666667;" width="167.7083" x="1043.75" y="1210.2295"/><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="134.375" x="1060.4167" y="1244.1976">9. &#25191;&#34892;&#24037;&#20316;&#20219;&#21153;&#65292;</text><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="116.6667" x="1060.4167" y="1265.1611">&#24182;&#35760;&#24405;&#20851;&#38190;&#25104;&#26524;</text><rect fill="none" height="626.8392" rx="10.4167" ry="10.4167" style="stroke:#A9A9A9;stroke-width:1.0416666666666667;" width="338.0208" x="1009.375" y="1371.2321"/><path d="M1107.2917,1371.2321 L1107.2917,1405.7373 L1096.875,1416.154 L1009.375,1416.154 " fill="none" style="stroke:#A9A9A9;stroke-width:1.0416666666666667;"/><text fill="#0000FF" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="66.6667" x="1022.9167" y="1399.9919">&#32489;&#25928;&#35780;&#20272;</text><rect fill="#FFFFFF" height="75.2604" rx="10.4167" ry="10.4167" style="stroke:#2C3E50;stroke-width:1.0416666666666667;" width="210.4167" x="1022.3958" y="1433.8623"/><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="177.0833" x="1039.0625" y="1467.8304">10. &#25552;&#20132;&#12298;&#32489;&#25928;&#33258;&#35780;&#34920;&#12299;</text><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="116.6667" x="1039.0625" y="1488.7939">&#21450;&#30456;&#20851;&#35777;&#26126;&#26448;&#26009;</text><rect fill="none" height="417.7083" rx="10.4167" ry="10.4167" style="stroke:#A9A9A9;stroke-width:1.0416666666666667;" width="308.3333" x="1025.5208" y="2031.7546"/><path d="M1123.4375,2031.7546 L1123.4375,2066.2598 L1113.0208,2076.6764 L1025.5208,2076.6764 " fill="none" style="stroke:#A9A9A9;stroke-width:1.0416666666666667;"/><text fill="#0000FF" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="66.6667" x="1039.0625" y="2060.5143">&#24212;&#29992;&#25191;&#34892;</text><rect fill="#FFFFFF" height="54.2969" rx="10.4167" ry="10.4167" style="stroke:#2C3E50;stroke-width:1.0416666666666667;" width="160.4167" x="1047.3958" y="2190.4785"/><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="127.0833" x="1064.0625" y="2224.4466">16. &#30830;&#35748;&#32489;&#25928;&#32467;&#26524;</text><line style="stroke:#9C27B0;stroke-width:2.0833333333333335;" x1="993.75" x2="993.75" y1="105.9733" y2="2491.1296"/><rect fill="#E6F3E6" height="2385.1563" style="stroke:#E6F3E6;stroke-width:1.0416666666666667;" width="517.1875" x="1351.5625" y="105.9733"/><rect fill="none" height="451.3916" rx="10.4167" ry="10.4167" style="stroke:#A9A9A9;stroke-width:1.0416666666666667;" width="494.2708" x="1357.8125" y="543.6279"/><path d="M1455.7292,543.6279 L1455.7292,578.1331 L1445.3125,588.5498 L1357.8125,588.5498 " fill="none" style="stroke:#A9A9A9;stroke-width:1.0416666666666667;"/><text fill="#0000FF" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="66.6667" x="1371.3542" y="572.3877">&#21046;&#23450;&#30446;&#26631;</text><rect fill="#FFFFFF" height="75.2604" rx="10.4167" ry="10.4167" style="stroke:#2C3E50;stroke-width:1.0416666666666667;" width="137.5" x="1416.6667" y="606.2581"/><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="101.0417" x="1433.3333" y="640.2262">4. &#21521;&#19979;&#23646;&#20256;&#36798;</text><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="104.1667" x="1433.3333" y="661.1898">&#37096;&#38376;/&#22242;&#38431;&#30446;&#26631;</text><path d="M1589.5833,788.029 L1589.5833,903.1331 L1841.6667,903.1331 L1841.6667,798.4456 L1831.25,788.029 L1589.5833,788.029 " fill="#FFC0CB" style="stroke:#2C3E50;stroke-width:1.0416666666666667;"/><path d="M1831.25,788.029 L1831.25,798.4456 L1841.6667,798.4456 L1831.25,788.029 " fill="#FFC0CB" style="stroke:#2C3E50;stroke-width:1.0416666666666667;"/><text fill="#FF0000" font-family="sans-serif" font-size="16.6667" font-weight="bold" lengthAdjust="spacing" textLength="75" x="1606.25" y="820.9554">&#26680;&#24515;&#30171;&#28857;:</text><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="142.7083" x="1606.25" y="841.9189">- &#30446;&#26631;&#35774;&#23450;&#20027;&#35266;&#24615;&#24378;</text><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="209.375" x="1606.25" y="862.8825">- &#27807;&#36890;&#35760;&#24405;&#22810;&#20026;&#21475;&#22836;&#25110;&#37038;&#20214;&#65292;</text><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="150" x="1614.5833" y="883.846">&#38590;&#20197;&#36861;&#28335;&#21644;&#32479;&#19968;&#31649;&#29702;</text><rect fill="#FFFFFF" height="75.2604" rx="10.4167" ry="10.4167" style="stroke:#2C3E50;stroke-width:1.0416666666666667;" width="166.6667" x="1402.0833" y="807.9508"/><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="117.7083" x="1418.75" y="841.9189">6. &#19982;&#21592;&#24037;&#27807;&#36890;&#24182;</text><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="133.3333" x="1418.75" y="862.8825">&#30830;&#35748;&#26368;&#32456;&#32489;&#25928;&#30446;&#26631;</text><rect fill="none" height="246.4844" rx="10.4167" ry="10.4167" style="stroke:#A9A9A9;stroke-width:1.0416666666666667;" width="420.8333" x="1391.1458" y="1051.5055"/><path d="M1489.0625,1051.5055 L1489.0625,1086.0107 L1478.6458,1096.4274 L1391.1458,1096.4274 " fill="none" style="stroke:#A9A9A9;stroke-width:1.0416666666666667;"/><text fill="#0000FF" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="66.6667" x="1404.6875" y="1080.2653">&#36807;&#31243;&#36319;&#36394;</text><path d="M1575.5208,1125.6592 L1575.5208,1147.5993 L1554.6875,1151.766 L1575.5208,1155.9326 L1575.5208,1177.8727 A0,0 0 0 0 1575.5208,1177.8727 L1801.5625,1177.8727 A0,0 0 0 0 1801.5625,1177.8727 L1801.5625,1136.0758 L1791.1458,1125.6592 L1575.5208,1125.6592 A0,0 0 0 0 1575.5208,1125.6592 " fill="#FFFFE0" style="stroke:#2C3E50;stroke-width:1.0416666666666667;"/><path d="M1791.1458,1125.6592 L1791.1458,1136.0758 L1801.5625,1136.0758 L1791.1458,1125.6592 " fill="#FFFFE0" style="stroke:#2C3E50;stroke-width:1.0416666666666667;"/><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="183.3333" x="1592.1875" y="1158.5856">&#35760;&#24405;&#20851;&#38190;&#20107;&#20214;&#21644;&#36741;&#23548;&#35201;&#28857;</text><rect fill="#FFFFFF" height="75.2604" rx="10.4167" ry="10.4167" style="stroke:#2C3E50;stroke-width:1.0416666666666667;" width="138.5417" x="1416.1458" y="1114.1357"/><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="105.2083" x="1432.8125" y="1148.1038">8. &#23450;&#26399;/&#19981;&#23450;&#26399;</text><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="100" x="1432.8125" y="1169.0674">&#36827;&#34892;&#32489;&#25928;&#36741;&#23548;</text><rect fill="none" height="626.8392" rx="10.4167" ry="10.4167" style="stroke:#A9A9A9;stroke-width:1.0416666666666667;" width="497.3958" x="1367.1875" y="1371.2321"/><path d="M1465.1042,1371.2321 L1465.1042,1405.7373 L1454.6875,1416.154 L1367.1875,1416.154 " fill="none" style="stroke:#A9A9A9;stroke-width:1.0416666666666667;"/><text fill="#0000FF" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="66.6667" x="1380.7292" y="1399.9919">&#32489;&#25928;&#35780;&#20272;</text><rect fill="#FFFFFF" height="75.2604" rx="10.4167" ry="10.4167" style="stroke:#2C3E50;stroke-width:1.0416666666666667;" width="160.4167" x="1405.2083" y="1529.9561"/><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="127.0833" x="1421.875" y="1563.9242">11. &#36827;&#34892;&#21021;&#27493;&#35780;&#23450;</text><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="110.4167" x="1421.875" y="1584.8877">(&#30452;&#32447;&#32463;&#29702;&#21021;&#35780;)</text><polygon fill="#F1C40F" points="1390.1042,1628.2145,1580.7292,1628.2145,1593.2292,1649.113,1580.7292,1670.0114,1390.1042,1670.0114,1377.6042,1649.113,1390.1042,1628.2145" style="stroke:#D35400;stroke-width:1.0416666666666667;"/><text fill="#008000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="27.0833" x="1500" y="1697.7295">Yes</text><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="169.7917" x="1400.5208" y="1655.9326">12. &#26159;&#21542;&#38656;&#35201;&#38548;&#32423;&#22797;&#26680;?</text><text fill="#FF0000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="20.8333" x="1603.6458" y="1635.0342">No</text><polygon fill="#F1C40F" points="1485.4167,1805.7536,1497.9167,1818.2536,1485.4167,1830.7536,1472.9167,1818.2536,1485.4167,1805.7536" style="stroke:#D35400;stroke-width:1.0416666666666667;"/><rect fill="none" height="417.7083" rx="10.4167" ry="10.4167" style="stroke:#A9A9A9;stroke-width:1.0416666666666667;" width="467.7083" x="1383.3333" y="2031.7546"/><path d="M1481.25,2031.7546 L1481.25,2066.2598 L1470.8333,2076.6764 L1383.3333,2076.6764 " fill="none" style="stroke:#A9A9A9;stroke-width:1.0416666666666667;"/><text fill="#0000FF" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="66.6667" x="1396.875" y="2060.5143">&#24212;&#29992;&#25191;&#34892;</text><rect fill="#FFFFFF" height="75.2604" rx="10.4167" ry="10.4167" style="stroke:#2C3E50;stroke-width:1.0416666666666667;" width="160.4167" x="1405.2083" y="2094.3848"/><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="127.0833" x="1421.875" y="2128.3529">15. &#36827;&#34892;&#32489;&#25928;&#38754;&#35848;</text><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="83.3333" x="1421.875" y="2149.3164">&#19982;&#32467;&#26524;&#21453;&#39304;</text><line style="stroke:#9C27B0;stroke-width:2.0833333333333335;" x1="1351.5625" x2="1351.5625" y1="105.9733" y2="2491.1296"/><line style="stroke:#9C27B0;stroke-width:2.0833333333333335;" x1="1868.75" x2="1868.75" y1="105.9733" y2="2491.1296"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="114.5833" x2="114.5833" y1="173.7956" y2="194.6289"/><polygon fill="#2C3E50" points="110.4167,184.2122,114.5833,194.6289,118.75,184.2122,114.5833,188.3789" style="stroke:#2C3E50;stroke-width:1.0416666666666667;"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="114.5833" x2="114.5833" y1="269.8893" y2="300.1628"/><polygon fill="#2C3E50" points="110.4167,289.7461,114.5833,300.1628,118.75,289.7461,114.5833,293.9128" style="stroke:#2C3E50;stroke-width:1.0416666666666667;"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="579.1667" x2="579.1667" y1="2340.8691" y2="2361.7025"/><polygon fill="#2C3E50" points="575,2351.2858,579.1667,2361.7025,583.3333,2351.2858,579.1667,2355.4525" style="stroke:#2C3E50;stroke-width:1.0416666666666667;"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="579.1667" x2="579.1667" y1="2436.9629" y2="2470.2962"/><polygon fill="#2C3E50" points="575,2459.8796,579.1667,2470.2962,583.3333,2459.8796,579.1667,2464.0462" style="stroke:#2C3E50;stroke-width:1.0416666666666667;"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="1127.6042" x2="1127.6042" y1="1285.4899" y2="1433.8623"/><polygon fill="#2C3E50" points="1123.4375,1423.4456,1127.6042,1433.8623,1131.7708,1423.4456,1127.6042,1427.6123" style="stroke:#2C3E50;stroke-width:1.0416666666666667;"/><text fill="#808080" font-family="sans-serif" font-size="41.6667" lengthAdjust="spacing" textLength="166.6667" x="1142.1875" y="1359.2529">&#32489;&#25928;&#35780;&#20272;</text><line style="stroke:#2C3E50;stroke-width:3.125;" x1="1593.2292" x2="1822.9167" y1="1649.113" y2="1649.113"/><polygon fill="#2C3E50" points="1818.75,1743.0908,1822.9167,1753.5075,1827.0833,1743.0908,1822.9167,1747.2575" style="stroke:#2C3E50;stroke-width:3.125;"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="1822.9167" x2="1822.9167" y1="1649.113" y2="1818.2536"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="1822.9167" x2="1497.9167" y1="1818.2536" y2="1818.2536"/><polygon fill="#2C3E50" points="1508.3333,1814.0869,1497.9167,1818.2536,1508.3333,1822.4202,1504.1667,1818.2536" style="stroke:#2C3E50;stroke-width:1.0416666666666667;"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="1485.4167" x2="1485.4167" y1="1605.2165" y2="1628.2145"/><polygon fill="#2C3E50" points="1481.25,1617.7979,1485.4167,1628.2145,1489.5833,1617.7979,1485.4167,1621.9645" style="stroke:#2C3E50;stroke-width:1.0416666666666667;"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="114.5833" x2="114.5833" y1="375.4232" y2="380.6315"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="114.5833" x2="579.1667" y1="380.6315" y2="380.6315"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="579.1667" x2="579.1667" y1="380.6315" y2="396.2565"/><polygon fill="#2C3E50" points="575,385.8398,579.1667,396.2565,583.3333,385.8398,579.1667,390.0065" style="stroke:#2C3E50;stroke-width:1.0416666666666667;"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="1485.4167" x2="1485.4167" y1="681.5186" y2="686.7269"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="1485.4167" x2="1127.6042" y1="686.7269" y2="686.7269"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="1127.6042" x2="1127.6042" y1="686.7269" y2="702.3519"/><polygon fill="#2C3E50" points="1123.4375,691.9352,1127.6042,702.3519,1131.7708,691.9352,1127.6042,696.1019" style="stroke:#2C3E50;stroke-width:1.0416666666666667;"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="1127.6042" x2="1127.6042" y1="777.6123" y2="782.8206"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="1127.6042" x2="1485.4167" y1="782.8206" y2="782.8206"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="1485.4167" x2="1485.4167" y1="782.8206" y2="807.9508"/><polygon fill="#2C3E50" points="1481.25,797.5342,1485.4167,807.9508,1489.5833,797.5342,1485.4167,801.7008" style="stroke:#2C3E50;stroke-width:1.0416666666666667;"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="1485.4167" x2="1485.4167" y1="883.2113" y2="908.3415"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="1485.4167" x2="1127.6042" y1="908.3415" y2="908.3415"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="1127.6042" x2="1127.6042" y1="908.3415" y2="923.9665"/><polygon fill="#2C3E50" points="1123.4375,913.5498,1127.6042,923.9665,1131.7708,913.5498,1127.6042,917.7165" style="stroke:#2C3E50;stroke-width:1.0416666666666667;"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="579.1667" x2="579.1667" y1="471.5169" y2="538.4196"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="579.1667" x2="1485.4167" y1="538.4196" y2="538.4196"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="1485.4167" x2="1485.4167" y1="538.4196" y2="606.2581"/><polygon fill="#2C3E50" points="1481.25,595.8415,1485.4167,606.2581,1489.5833,595.8415,1485.4167,600.0081" style="stroke:#2C3E50;stroke-width:1.0416666666666667;"/><text fill="#808080" font-family="sans-serif" font-size="41.6667" lengthAdjust="spacing" textLength="166.6667" x="593.75" y="531.6488">&#21046;&#23450;&#30446;&#26631;</text><line style="stroke:#2C3E50;stroke-width:3.125;" x1="1485.4167" x2="1485.4167" y1="1189.3962" y2="1194.6045"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="1485.4167" x2="1127.6042" y1="1194.6045" y2="1194.6045"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="1127.6042" x2="1127.6042" y1="1194.6045" y2="1210.2295"/><polygon fill="#2C3E50" points="1123.4375,1199.8128,1127.6042,1210.2295,1131.7708,1199.8128,1127.6042,1203.9795" style="stroke:#2C3E50;stroke-width:1.0416666666666667;"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="1127.6042" x2="1127.6042" y1="978.2633" y2="1046.2972"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="1127.6042" x2="1485.4167" y1="1046.2972" y2="1046.2972"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="1485.4167" x2="1485.4167" y1="1046.2972" y2="1114.1357"/><polygon fill="#2C3E50" points="1481.25,1103.7191,1485.4167,1114.1357,1489.5833,1103.7191,1485.4167,1107.8857" style="stroke:#2C3E50;stroke-width:1.0416666666666667;"/><text fill="#808080" font-family="sans-serif" font-size="41.6667" lengthAdjust="spacing" textLength="166.6667" x="1142.1875" y="1039.5264">&#36807;&#31243;&#36319;&#36394;</text><line style="stroke:#2C3E50;stroke-width:3.125;" x1="1127.6042" x2="1127.6042" y1="1509.1227" y2="1514.3311"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="1127.6042" x2="1485.4167" y1="1514.3311" y2="1514.3311"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="1485.4167" x2="1485.4167" y1="1514.3311" y2="1529.9561"/><polygon fill="#2C3E50" points="1481.25,1519.5394,1485.4167,1529.9561,1489.5833,1519.5394,1485.4167,1523.7061" style="stroke:#2C3E50;stroke-width:1.0416666666666667;"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="1485.4167" x2="1485.4167" y1="1670.0114" y2="1689.8356"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="1485.4167" x2="114.5833" y1="1689.8356" y2="1689.8356"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="114.5833" x2="114.5833" y1="1689.8356" y2="1709.6598"/><polygon fill="#2C3E50" points="110.4167,1699.2432,114.5833,1709.6598,118.75,1699.2432,114.5833,1703.4098" style="stroke:#2C3E50;stroke-width:1.0416666666666667;"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="114.5833" x2="114.5833" y1="1784.9202" y2="1790.1286"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="114.5833" x2="1485.4167" y1="1790.1286" y2="1790.1286"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="1485.4167" x2="1485.4167" y1="1790.1286" y2="1805.7536"/><polygon fill="#2C3E50" points="1481.25,1795.3369,1485.4167,1805.7536,1489.5833,1795.3369,1485.4167,1799.5036" style="stroke:#2C3E50;stroke-width:1.0416666666666667;"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="1485.4167" x2="1485.4167" y1="1830.7536" y2="1838.1429"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="1485.4167" x2="579.1667" y1="1838.1429" y2="1838.1429"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="579.1667" x2="579.1667" y1="1838.1429" y2="1871.5739"/><polygon fill="#2C3E50" points="575,1861.1572,579.1667,1871.5739,583.3333,1861.1572,579.1667,1865.3239" style="stroke:#2C3E50;stroke-width:1.0416666666666667;"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="1485.4167" x2="1485.4167" y1="2169.6452" y2="2174.8535"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="1485.4167" x2="1127.6042" y1="2174.8535" y2="2174.8535"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="1127.6042" x2="1127.6042" y1="2174.8535" y2="2190.4785"/><polygon fill="#2C3E50" points="1123.4375,2180.0618,1127.6042,2190.4785,1131.7708,2180.0618,1127.6042,2184.2285" style="stroke:#2C3E50;stroke-width:1.0416666666666667;"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="1127.6042" x2="1127.6042" y1="2244.7754" y2="2249.9837"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="1127.6042" x2="579.1667" y1="2249.9837" y2="2249.9837"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="579.1667" x2="579.1667" y1="2249.9837" y2="2265.6087"/><polygon fill="#2C3E50" points="575,2255.1921,579.1667,2265.6087,583.3333,2255.1921,579.1667,2259.3587" style="stroke:#2C3E50;stroke-width:1.0416666666666667;"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="579.1667" x2="579.1667" y1="1946.8343" y2="2026.5462"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="579.1667" x2="1485.4167" y1="2026.5462" y2="2026.5462"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="1485.4167" x2="1485.4167" y1="2026.5462" y2="2094.3848"/><polygon fill="#2C3E50" points="1481.25,2083.9681,1485.4167,2094.3848,1489.5833,2083.9681,1485.4167,2088.1348" style="stroke:#2C3E50;stroke-width:1.0416666666666667;"/><text fill="#808080" font-family="sans-serif" font-size="41.6667" lengthAdjust="spacing" textLength="166.6667" x="593.75" y="2019.7754">&#24212;&#29992;&#25191;&#34892;</text><text fill="#2196F3" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="66.6667" x="203.6458" y="133.6914">&#31649;&#29702;&#20013;&#24515;</text><text fill="#2196F3" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="83.3333" x="684.375" y="133.6914">&#20154;&#20107;&#34892;&#25919;&#37096;</text><text fill="#2196F3" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="33.3333" x="1155.9896" y="133.6914">&#21592;&#24037;</text><text fill="#2196F3" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="147.9167" x="1536.1979" y="133.6914">&#29992;&#20154;&#37096;&#38376; (&#30452;&#23646;&#32463;&#29702;)</text></g></svg>`,
            file2_9bb592: `<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" contentStyleType="text/css" height="2934.375px" preserveAspectRatio="none" style="width:2214px;height:2934px;" version="1.1" viewBox="0 0 2214 2934" width="2214.5833px" zoomAndPan="magnify"><defs><linearGradient id="ghu640n689csu0" x1="50%" x2="50%" y1="0%" y2="100%"><stop offset="0%" stop-color="#FFFFFF"/><stop offset="100%" stop-color="#FFFFFF"/></linearGradient></defs><g><rect fill="url(#ghu640n689csu0)" height="72.64" id="_title" rx="10.4167" ry="10.4167" style="stroke:#CCCCCC;stroke-width:1.0416666666666667;" width="258.9193" x="976.5299" y="20.8333"/><text fill="#000000" font-family="sans-serif" font-size="31.25" font-weight="bold" lengthAdjust="spacing" textLength="225.5859" x="993.1966" y="69.9402">&#32771;&#21220;&#19982;&#25490;&#29677;&#27969;&#31243;</text><rect fill="url(#ghu640n689csu0)" height="41.7806" style="stroke:url(#ghu640n689csu0);stroke-width:1.0416666666666667;" width="2180.7292" x="15.625" y="105.9733"/><rect fill="#E6F3E6" height="2816.6504" style="stroke:#E6F3E6;stroke-width:1.0416666666666667;" width="301.5625" x="15.625" y="105.9733"/><rect fill="none" height="626.5381" rx="10.4167" ry="10.4167" style="stroke:#A9A9A9;stroke-width:1.0416666666666667;" width="275.5208" x="25" y="623.9014"/><path d="M206.25,623.9014 L206.25,658.4066 L195.8333,668.8232 L25,668.8232 " fill="none" style="stroke:#A9A9A9;stroke-width:1.0416666666666667;"/><text fill="#0000FF" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="150" x="38.5417" y="652.6611">&#26085;&#24120;&#32771;&#21220;&#19982;&#24322;&#24120;&#22788;&#29702;</text><rect fill="#FFFFFF" height="75.2604" rx="10.4167" ry="10.4167" style="stroke:#2C3E50;stroke-width:1.0416666666666667;" width="184.375" x="72.9167" y="686.5316"/><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="151.0417" x="89.5833" y="720.4997">4. &#25353;&#25490;&#29677;&#34920;&#19978;&#19979;&#29677;&#65292;</text><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="100" x="89.5833" y="741.4632">&#22312;&#32771;&#21220;&#26426;&#25171;&#21345;</text><rect fill="#FFFFFF" height="75.2604" rx="10.4167" ry="10.4167" style="stroke:#2C3E50;stroke-width:1.0416666666666667;" width="171.875" x="79.1667" y="866.2354"/><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="138.5417" x="95.8333" y="900.2035">6. &#22635;&#20889;&#32440;&#36136;&#12298;&#35831;&#20551;/</text><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="100" x="95.8333" y="921.167">&#21152;&#29677;&#30003;&#35831;&#21333;&#12299;</text><polygon fill="#F1C40F" points="82.8125,784.79,247.3958,784.79,259.8958,805.6885,247.3958,826.5869,82.8125,826.5869,70.3125,805.6885,82.8125,784.79" style="stroke:#D35400;stroke-width:1.0416666666666667;"/><text fill="#008000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="27.0833" x="179.6875" y="854.305">Yes</text><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="143.75" x="93.2292" y="812.5081">5. &#26159;&#21542;&#26377;&#24322;&#24120;&#24773;&#20917;?</text><text fill="#FF0000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="20.8333" x="39.0625" y="791.6097">No</text><polygon fill="#F1C40F" points="165.1042,1112.5895,177.6042,1125.0895,165.1042,1137.5895,152.6042,1125.0895,165.1042,1112.5895" style="stroke:#D35400;stroke-width:1.0416666666666667;"/><rect fill="none" height="558.61" rx="10.4167" ry="10.4167" style="stroke:#A9A9A9;stroke-width:1.0416666666666667;" width="277.0833" x="35.9375" y="2322.347"/><path d="M133.8542,2322.347 L133.8542,2356.8522 L123.4375,2367.2689 L35.9375,2367.2689 " fill="none" style="stroke:#A9A9A9;stroke-width:1.0416666666666667;"/><text fill="#0000FF" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="66.6667" x="49.4792" y="2351.1068">&#32467;&#26524;&#24212;&#29992;</text><rect fill="#FFFFFF" height="75.2604" rx="10.4167" ry="10.4167" style="stroke:#2C3E50;stroke-width:1.0416666666666667;" width="183.3333" x="73.4375" y="2567.7897"/><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="143.75" x="90.1042" y="2601.7578">21. &#22312;&#24037;&#36164;&#26465;&#20013;&#26597;&#30475;</text><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="150" x="90.1042" y="2622.7214">&#26368;&#32456;&#32771;&#21220;&#19982;&#34218;&#36164;&#32467;&#26524;</text><rect fill="#D3D3D3" height="54.2969" rx="10.4167" ry="10.4167" style="stroke:#2C3E50;stroke-width:1.0416666666666667;" width="216.6667" x="56.7708" y="2747.4935"/><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="183.3333" x="73.4375" y="2781.4616">23. &#21521;&#20027;&#31649;&#25110;HR&#21457;&#36215;&#30003;&#35785;</text><ellipse cx="165.1042" cy="2834.082" fill="none" rx="11.4583" ry="11.4583" style="stroke:#1A78C2;stroke-width:1.0416666666666667;"/><ellipse cx="165.1042" cy="2834.082" fill="none" rx="6.25" ry="6.25" style="stroke:#1A78C2;stroke-width:1.0416666666666667;"/><polygon fill="#F1C40F" points="86.4583,2666.0482,243.75,2666.0482,256.25,2686.9466,243.75,2707.8451,86.4583,2707.8451,73.9583,2686.9466,86.4583,2666.0482" style="stroke:#D35400;stroke-width:1.0416666666666667;"/><text fill="#008000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="27.0833" x="179.6875" y="2735.5632">Yes</text><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="136.4583" x="96.875" y="2693.7663">22. &#23545;&#32467;&#26524;&#26377;&#24322;&#35758;?</text><text fill="#FF0000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="20.8333" x="266.6667" y="2672.8678">No</text><ellipse cx="165.1042" cy="2912.207" fill="none" rx="10.4167" ry="10.4167" style="stroke:#9C27B0;stroke-width:1.5625;"/><line style="stroke:#9C27B0;stroke-width:2.604166666666667;" x1="158.6592" x2="171.5492" y1="2905.762" y2="2918.652"/><line style="stroke:#9C27B0;stroke-width:2.604166666666667;" x1="171.5492" x2="158.6592" y1="2905.762" y2="2918.652"/><line style="stroke:#9C27B0;stroke-width:2.0833333333333335;" x1="15.625" x2="15.625" y1="105.9733" y2="2922.6237"/><rect fill="#E6E6FA" height="2816.6504" style="stroke:#E6E6FA;stroke-width:1.0416666666666667;" width="548.9583" x="317.1875" y="105.9733"/><rect fill="none" height="325.8708" rx="10.4167" ry="10.4167" style="stroke:#A9A9A9;stroke-width:1.0416666666666667;" width="400" x="357.2917" y="241.5446"/><path d="M505.2083,241.5446 L505.2083,276.0498 L494.7917,286.4665 L357.2917,286.4665 " fill="none" style="stroke:#A9A9A9;stroke-width:1.0416666666666667;"/><text fill="#0000FF" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="116.6667" x="370.8333" y="270.3044">&#35268;&#21017;&#21046;&#23450;&#19982;&#25490;&#29677;</text><path d="M564.5833,411.792 L564.5833,433.7321 L543.75,437.8988 L564.5833,442.0654 L564.5833,464.0055 A0,0 0 0 0 564.5833,464.0055 L746.875,464.0055 A0,0 0 0 0 746.875,464.0055 L746.875,422.2087 L736.4583,411.792 L564.5833,411.792 A0,0 0 0 0 564.5833,411.792 " fill="#FFFFE0" style="stroke:#2C3E50;stroke-width:1.0416666666666667;"/><path d="M736.4583,411.792 L736.4583,422.2087 L746.875,422.2087 L736.4583,411.792 " fill="#FFFFE0" style="stroke:#2C3E50;stroke-width:1.0416666666666667;"/><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="139.5833" x="581.25" y="444.7184">&#36890;&#24120;&#20351;&#29992;Excel&#27169;&#26495;</text><rect fill="#FFFFFF" height="75.2604" rx="10.4167" ry="10.4167" style="stroke:#2C3E50;stroke-width:1.0416666666666667;" width="167.7083" x="376.0417" y="400.2686"/><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="134.375" x="392.7083" y="434.2367">2. &#26681;&#25454;&#19994;&#21153;&#38656;&#27714;&#65292;</text><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="116.6667" x="392.7083" y="455.2002">&#32534;&#21046;&#19979;&#26376;&#25490;&#29677;&#34920;</text><rect fill="#FFFFFF" height="54.2969" rx="10.4167" ry="10.4167" style="stroke:#2C3E50;stroke-width:1.0416666666666667;" width="184.375" x="367.7083" y="496.3623"/><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="151.0417" x="384.375" y="530.3304">3. &#19979;&#21457;&#25490;&#29677;&#34920;&#32473;&#21592;&#24037;</text><rect fill="none" height="626.5381" rx="10.4167" ry="10.4167" style="stroke:#A9A9A9;stroke-width:1.0416666666666667;" width="289.0625" x="323.4375" y="623.9014"/><path d="M504.6875,623.9014 L504.6875,658.4066 L494.2708,668.8232 L323.4375,668.8232 " fill="none" style="stroke:#A9A9A9;stroke-width:1.0416666666666667;"/><text fill="#0000FF" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="150" x="336.9792" y="652.6611">&#26085;&#24120;&#32771;&#21220;&#19982;&#24322;&#24120;&#22788;&#29702;</text><rect fill="#FFFFFF" height="54.2969" rx="10.4167" ry="10.4167" style="stroke:#2C3E50;stroke-width:1.0416666666666667;" width="238.5417" x="340.625" y="962.3291"/><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="205.2083" x="357.2917" y="996.2972">7. &#23457;&#25209;&#12298;&#35831;&#20551;/&#21152;&#29677;&#30003;&#35831;&#21333;&#12299;</text><rect fill="none" height="953.9551" rx="10.4167" ry="10.4167" style="stroke:#A9A9A9;stroke-width:1.0416666666666667;" width="526.0417" x="335.9375" y="1306.9255"/><path d="M483.8542,1306.9255 L483.8542,1341.4307 L473.4375,1351.8473 L335.9375,1351.8473 " fill="none" style="stroke:#A9A9A9;stroke-width:1.0416666666666667;"/><text fill="#0000FF" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="116.6667" x="349.4792" y="1335.6852">&#25968;&#25454;&#27719;&#24635;&#19982;&#26680;&#31639;</text><rect fill="#FFFFFF" height="75.2604" rx="10.4167" ry="10.4167" style="stroke:#2C3E50;stroke-width:1.0416666666666667;" width="227.0833" x="346.3542" y="1687.264"/><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="193.75" x="363.0208" y="1721.2321">13. &#25509;&#25910;&#24182;&#23457;&#26680;&#37096;&#38376;&#21592;&#24037;&#30340;</text><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="133.3333" x="363.0208" y="1742.1956">&#12298;&#32771;&#21220;&#26376;&#25253;&#12299;&#33609;&#31295;</text><rect fill="#FFFFFF" height="75.2604" rx="10.4167" ry="10.4167" style="stroke:#2C3E50;stroke-width:1.0416666666666667;" width="150" x="384.8958" y="1866.9678"/><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="116.6667" x="401.5625" y="1900.9359">15. &#19982;HR&#27807;&#36890;&#65292;</text><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="100" x="401.5625" y="1921.8994">&#36827;&#34892;&#25968;&#25454;&#20462;&#27491;</text><polygon fill="#F1C40F" points="381.25,1785.5225,538.5417,1785.5225,551.0417,1806.4209,538.5417,1827.3193,381.25,1827.3193,368.75,1806.4209,381.25,1785.5225" style="stroke:#D35400;stroke-width:1.0416666666666667;"/><text fill="#008000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="27.0833" x="474.4792" y="1855.0374">Yes</text><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="136.4583" x="391.6667" y="1813.2406">14. &#21457;&#29616;&#25968;&#25454;&#26377;&#35823;?</text><text fill="#FF0000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="20.8333" x="337.5" y="1792.3421">No</text><polygon fill="#F1C40F" points="459.8958,2052.1566,472.3958,2064.6566,459.8958,2077.1566,447.3958,2064.6566,459.8958,2052.1566" style="stroke:#D35400;stroke-width:1.0416666666666667;"/><rect fill="#98FB98" height="54.2969" rx="10.4167" ry="10.4167" style="stroke:#2C3E50;stroke-width:1.0416666666666667;" width="210.4167" x="354.6875" y="2097.9899"/><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="177.0833" x="371.3542" y="2131.958">17. &#30830;&#35748;&#26412;&#37096;&#38376;&#32771;&#21220;&#32467;&#26524;</text><line style="stroke:#9C27B0;stroke-width:2.0833333333333335;" x1="317.1875" x2="317.1875" y1="105.9733" y2="2922.6237"/><rect fill="#FFFACD" height="2816.6504" style="stroke:#FFFACD;stroke-width:1.0416666666666667;" width="590.625" x="866.1458" y="105.9733"/><ellipse cx="1005.7292" cy="163.3789" fill="#9C27B0" rx="10.4167" ry="10.4167" style="stroke:#1A78C2;stroke-width:1.0416666666666667;"/><rect fill="none" height="325.8708" rx="10.4167" ry="10.4167" style="stroke:#A9A9A9;stroke-width:1.0416666666666667;" width="400" x="903.125" y="241.5446"/><path d="M1051.0417,241.5446 L1051.0417,276.0498 L1040.625,286.4665 L903.125,286.4665 " fill="none" style="stroke:#A9A9A9;stroke-width:1.0416666666666667;"/><text fill="#0000FF" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="116.6667" x="916.6667" y="270.3044">&#35268;&#21017;&#21046;&#23450;&#19982;&#25490;&#29677;</text><path d="M1112.5,305.2165 L1112.5,337.6383 L1091.6667,341.805 L1112.5,345.9717 L1112.5,378.3936 A0,0 0 0 0 1112.5,378.3936 L1288.5417,378.3936 A0,0 0 0 0 1288.5417,378.3936 L1288.5417,315.6331 L1278.125,305.2165 L1112.5,305.2165 A0,0 0 0 0 1112.5,305.2165 " fill="#FFFFE0" style="stroke:#2C3E50;stroke-width:1.0416666666666667;"/><path d="M1278.125,305.2165 L1278.125,315.6331 L1288.5417,315.6331 L1278.125,305.2165 " fill="#FFFFE0" style="stroke:#2C3E50;stroke-width:1.0416666666666667;"/><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="133.3333" x="1129.1667" y="338.1429">&#36890;&#24120;&#20197;&#20844;&#21496;&#20844;&#21578;&#25110;</text><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="100" x="1129.1667" y="359.1064">&#37038;&#20214;&#24418;&#24335;&#21457;&#24067;</text><rect fill="#FFFFFF" height="75.2604" rx="10.4167" ry="10.4167" style="stroke:#2C3E50;stroke-width:1.0416666666666667;" width="171.875" x="919.7917" y="304.1748"/><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="138.5417" x="936.4583" y="338.1429">1. &#21046;&#23450;/&#26356;&#26032;&#32771;&#21220;&#19982;</text><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="66.6667" x="936.4583" y="359.1064">&#20551;&#26399;&#35268;&#21017;</text><rect fill="none" height="626.5381" rx="10.4167" ry="10.4167" style="stroke:#A9A9A9;stroke-width:1.0416666666666667;" width="285.9375" x="872.3958" y="623.9014"/><path d="M1053.6458,623.9014 L1053.6458,658.4066 L1043.2292,668.8232 L872.3958,668.8232 " fill="none" style="stroke:#A9A9A9;stroke-width:1.0416666666666667;"/><text fill="#0000FF" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="150" x="885.9375" y="652.6611">&#26085;&#24120;&#32771;&#21220;&#19982;&#24322;&#24120;&#22788;&#29702;</text><rect fill="#FFFFFF" height="54.2969" rx="10.4167" ry="10.4167" style="stroke:#2C3E50;stroke-width:1.0416666666666667;" width="184.375" x="913.5417" y="1037.4593"/><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="151.0417" x="930.2083" y="1071.4274">8. &#23384;&#26723;&#24050;&#23457;&#25209;&#30340;&#21333;&#25454;</text><rect fill="none" height="953.9551" rx="10.4167" ry="10.4167" style="stroke:#A9A9A9;stroke-width:1.0416666666666667;" width="567.7083" x="884.8958" y="1306.9255"/><path d="M1032.8125,1306.9255 L1032.8125,1341.4307 L1022.3958,1351.8473 L884.8958,1351.8473 " fill="none" style="stroke:#A9A9A9;stroke-width:1.0416666666666667;"/><text fill="#0000FF" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="116.6667" x="898.4375" y="1335.6852">&#25968;&#25454;&#27719;&#24635;&#19982;&#26680;&#31639;</text><path d="M1115.1042,1381.0791 L1115.1042,1403.0192 L1094.2708,1407.1859 L1115.1042,1411.3525 L1115.1042,1433.2926 A0,0 0 0 0 1115.1042,1433.2926 L1324.4792,1433.2926 A0,0 0 0 0 1324.4792,1433.2926 L1324.4792,1391.4958 L1314.0625,1381.0791 L1115.1042,1381.0791 A0,0 0 0 0 1115.1042,1381.0791 " fill="#FFFFE0" style="stroke:#2C3E50;stroke-width:1.0416666666666667;"/><path d="M1314.0625,1381.0791 L1314.0625,1391.4958 L1324.4792,1391.4958 L1314.0625,1381.0791 " fill="#FFFFE0" style="stroke:#2C3E50;stroke-width:1.0416666666666667;"/><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="166.6667" x="1131.7708" y="1414.0055">&#36890;&#24120;&#20026; .dat &#25110; .xls &#25991;&#20214;</text><rect fill="#FFFFFF" height="75.2604" rx="10.4167" ry="10.4167" style="stroke:#2C3E50;stroke-width:1.0416666666666667;" width="177.0833" x="917.1875" y="1369.5557"/><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="143.75" x="933.8542" y="1403.5238">10. &#31532;&#20108;&#22825;&#20174;&#32771;&#21220;&#26426;</text><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="133.3333" x="933.8542" y="1424.4873">&#23548;&#20986;&#21407;&#22987;&#25171;&#21345;&#25968;&#25454;</text><path d="M1131.7708,1455.2327 L1131.7708,1570.3369 L1442.1875,1570.3369 L1442.1875,1465.6494 L1431.7708,1455.2327 L1131.7708,1455.2327 " fill="#FFC0CB" style="stroke:#2C3E50;stroke-width:1.0416666666666667;"/><path d="M1431.7708,1455.2327 L1431.7708,1465.6494 L1442.1875,1465.6494 L1431.7708,1455.2327 " fill="#FFC0CB" style="stroke:#2C3E50;stroke-width:1.0416666666666667;"/><text fill="#FF0000" font-family="sans-serif" font-size="16.6667" font-weight="bold" lengthAdjust="spacing" textLength="75" x="1148.4375" y="1488.1592">&#26680;&#24515;&#30171;&#28857;:</text><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="251.0417" x="1148.4375" y="1509.1227">1. &#25968;&#25454;&#26680;&#23545;&#32791;&#26102;&#24040;&#22823;&#65292;&#26497;&#26131;&#20986;&#38169;&#12290;</text><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="234.375" x="1148.4375" y="1530.0863">2. &#20551;&#26399;&#12289;&#21152;&#29677;&#24037;&#26102;&#38656;&#25163;&#21160;&#35745;&#31639;&#12290;</text><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="267.7083" x="1148.4375" y="1551.0498">3. &#35268;&#21017;&#22797;&#26434;&#26102;&#65292;&#20154;&#24037;&#26680;&#31639;&#21487;&#38752;&#24615;&#20302;&#12290;</text><rect fill="#FFFFFF" height="75.2604" rx="10.4167" ry="10.4167" style="stroke:#2C3E50;stroke-width:1.0416666666666667;" width="210.4167" x="900.5208" y="1475.1546"/><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="177.0833" x="917.1875" y="1509.1227">11. &#25163;&#21160;&#25972;&#29702;&#25171;&#21345;&#25968;&#25454;&#65292;</text><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="166.6667" x="917.1875" y="1530.0863">&#24182;&#19982;&#32440;&#36136;&#21333;&#25454;&#36827;&#34892;&#26680;&#23545;</text><rect fill="#FFFFFF" height="75.2604" rx="10.4167" ry="10.4167" style="stroke:#2C3E50;stroke-width:1.0416666666666667;" width="166.6667" x="922.3958" y="1591.1702"/><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="93.75" x="939.0625" y="1625.1383">12. &#21021;&#27493;&#29983;&#25104;</text><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="133.3333" x="939.0625" y="1646.1019">&#12298;&#32771;&#21220;&#26376;&#25253;&#12299;&#33609;&#31295;</text><rect fill="#FFFFFF" height="54.2969" rx="10.4167" ry="10.4167" style="stroke:#2C3E50;stroke-width:1.0416666666666667;" width="193.75" x="908.8542" y="1977.0264"/><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="160.4167" x="925.5208" y="2010.9945">16. &#26681;&#25454;&#21453;&#39304;&#20462;&#25913;&#25968;&#25454;</text><rect fill="#FFFFFF" height="75.2604" rx="10.4167" ry="10.4167" style="stroke:#2C3E50;stroke-width:1.0416666666666667;" width="216.6667" x="897.3958" y="2173.1201"/><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="177.0833" x="914.0625" y="2207.0882">18. &#27719;&#24635;&#25152;&#26377;&#37096;&#38376;&#30830;&#35748;&#30340;</text><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="183.3333" x="914.0625" y="2228.0518">&#32771;&#21220;&#32467;&#26524;&#65292;&#29983;&#25104;&#26368;&#32456;&#25253;&#34920;</text><line style="stroke:#9C27B0;stroke-width:2.0833333333333335;" x1="866.1458" x2="866.1458" y1="105.9733" y2="2922.6237"/><rect fill="#E0FFFF" height="2816.6504" style="stroke:#E0FFFF;stroke-width:1.0416666666666667;" width="267.7083" x="1456.7708" y="105.9733"/><rect fill="none" height="626.5381" rx="10.4167" ry="10.4167" style="stroke:#A9A9A9;stroke-width:1.0416666666666667;" width="257.2917" x="1463.0208" y="623.9014"/><path d="M1644.2708,623.9014 L1644.2708,658.4066 L1633.8542,668.8232 L1463.0208,668.8232 " fill="none" style="stroke:#A9A9A9;stroke-width:1.0416666666666667;"/><text fill="#0000FF" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="150" x="1476.5625" y="652.6611">&#26085;&#24120;&#32771;&#21220;&#19982;&#24322;&#24120;&#22788;&#29702;</text><rect fill="#FFFFFF" height="75.2604" rx="10.4167" ry="10.4167" style="stroke:#2C3E50;stroke-width:1.0416666666666667;" width="184.375" x="1475.5208" y="1158.4229"/><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="151.0417" x="1492.1875" y="1192.391">9. &#23450;&#26399;&#32500;&#25252;&#32771;&#21220;&#26426;&#65292;</text><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="100" x="1492.1875" y="1213.3545">&#30830;&#20445;&#27491;&#24120;&#36816;&#34892;</text><line style="stroke:#9C27B0;stroke-width:2.0833333333333335;" x1="1456.7708" x2="1456.7708" y1="105.9733" y2="2922.6237"/><rect fill="#FFF0F5" height="2816.6504" style="stroke:#FFF0F5;stroke-width:1.0416666666666667;" width="460.4167" x="1724.4792" y="105.9733"/><rect fill="none" height="558.61" rx="10.4167" ry="10.4167" style="stroke:#A9A9A9;stroke-width:1.0416666666666667;" width="450" x="1730.7292" y="2322.347"/><path d="M1828.6458,2322.347 L1828.6458,2356.8522 L1818.2292,2367.2689 L1730.7292,2367.2689 " fill="none" style="stroke:#A9A9A9;stroke-width:1.0416666666666667;"/><text fill="#0000FF" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="66.6667" x="1744.2708" y="2351.1068">&#32467;&#26524;&#24212;&#29992;</text><path d="M1994.2708,2377.6855 L1994.2708,2420.5892 L1973.4375,2424.7559 L1994.2708,2428.9225 L1994.2708,2471.8262 A0,0 0 0 0 1994.2708,2471.8262 L2170.3125,2471.8262 A0,0 0 0 0 2170.3125,2471.8262 L2170.3125,2388.1022 L2159.8958,2377.6855 L1994.2708,2377.6855 A0,0 0 0 0 1994.2708,2377.6855 " fill="#FFFFE0" style="stroke:#2C3E50;stroke-width:1.0416666666666667;"/><path d="M2159.8958,2377.6855 L2159.8958,2388.1022 L2170.3125,2388.1022 L2159.8958,2377.6855 " fill="#FFFFE0" style="stroke:#2C3E50;stroke-width:1.0416666666666667;"/><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="133.3333" x="2010.9375" y="2410.612">&#29992;&#20110;&#35745;&#31639;&#24037;&#36164;&#20013;&#30340;</text><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="133.3333" x="2010.9375" y="2431.5755">&#20840;&#21220;&#22870;&#12289;&#21152;&#29677;&#36153;&#12289;</text><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="116.6667" x="2010.9375" y="2452.5391">&#20551;&#26399;&#25187;&#27454;&#31561;&#39033;&#30446;</text><rect fill="#FFFFFF" height="54.2969" rx="10.4167" ry="10.4167" style="stroke:#2C3E50;stroke-width:1.0416666666666667;" width="227.0833" x="1746.3542" y="2397.6074"/><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="193.75" x="1763.0208" y="2431.5755">19. &#25509;&#25910;&#26368;&#32456;&#12298;&#32771;&#21220;&#26376;&#25253;&#12299;</text><rect fill="#FFFFFF" height="54.2969" rx="10.4167" ry="10.4167" style="stroke:#2C3E50;stroke-width:1.0416666666666667;" width="160.4167" x="1779.6875" y="2492.6595"/><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="127.0833" x="1796.3542" y="2526.6276">20. &#36827;&#34892;&#34218;&#36164;&#26680;&#31639;</text><line style="stroke:#9C27B0;stroke-width:2.0833333333333335;" x1="1724.4792" x2="1724.4792" y1="105.9733" y2="2922.6237"/><line style="stroke:#9C27B0;stroke-width:2.0833333333333335;" x1="2184.8958" x2="2184.8958" y1="105.9733" y2="2922.6237"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="165.1042" x2="165.1042" y1="826.5869" y2="866.2354"/><polygon fill="#2C3E50" points="160.9375,855.8187,165.1042,866.2354,169.2708,855.8187,165.1042,859.9854" style="stroke:#2C3E50;stroke-width:1.0416666666666667;"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="70.3125" x2="35.4167" y1="805.6885" y2="805.6885"/><polygon fill="#2C3E50" points="31.25,974.7965,35.4167,985.2132,39.5833,974.7965,35.4167,978.9632" style="stroke:#2C3E50;stroke-width:3.125;"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="35.4167" x2="35.4167" y1="805.6885" y2="1125.0895"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="35.4167" x2="152.6042" y1="1125.0895" y2="1125.0895"/><polygon fill="#2C3E50" points="142.1875,1120.9229,152.6042,1125.0895,142.1875,1129.2562,146.3542,1125.0895" style="stroke:#2C3E50;stroke-width:1.0416666666666667;"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="165.1042" x2="165.1042" y1="761.792" y2="784.79"/><polygon fill="#2C3E50" points="160.9375,774.3734,165.1042,784.79,169.2708,774.3734,165.1042,778.54" style="stroke:#2C3E50;stroke-width:1.0416666666666667;"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="165.1042" x2="165.1042" y1="2801.7904" y2="2822.6237"/><polygon fill="#2C3E50" points="160.9375,2812.207,165.1042,2822.6237,169.2708,2812.207,165.1042,2816.3737" style="stroke:#2C3E50;stroke-width:1.0416666666666667;"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="165.1042" x2="165.1042" y1="2707.8451" y2="2747.4935"/><polygon fill="#2C3E50" points="160.9375,2737.0768,165.1042,2747.4935,169.2708,2737.0768,165.1042,2741.2435" style="stroke:#2C3E50;stroke-width:1.0416666666666667;"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="256.25" x2="283.8542" y1="2686.9466" y2="2686.9466"/><polygon fill="#2C3E50" points="279.6875,2786.5885,283.8542,2797.0052,288.0208,2786.5885,283.8542,2790.7552" style="stroke:#2C3E50;stroke-width:3.125;"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="283.8542" x2="283.8542" y1="2686.9466" y2="2868.457"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="283.8542" x2="165.1042" y1="2868.457" y2="2868.457"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="165.1042" x2="165.1042" y1="2868.457" y2="2901.7904"/><polygon fill="#2C3E50" points="160.9375,2891.3737,165.1042,2901.7904,169.2708,2891.3737,165.1042,2895.5404" style="stroke:#2C3E50;stroke-width:1.0416666666666667;"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="165.1042" x2="165.1042" y1="2643.0501" y2="2666.0482"/><polygon fill="#2C3E50" points="160.9375,2655.6315,165.1042,2666.0482,169.2708,2655.6315,165.1042,2659.7982" style="stroke:#2C3E50;stroke-width:1.0416666666666667;"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="459.8958" x2="459.8958" y1="475.529" y2="496.3623"/><polygon fill="#2C3E50" points="455.7292,485.9456,459.8958,496.3623,464.0625,485.9456,459.8958,490.1123" style="stroke:#2C3E50;stroke-width:1.0416666666666667;"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="459.8958" x2="459.8958" y1="1827.3193" y2="1866.9678"/><polygon fill="#2C3E50" points="455.7292,1856.5511,459.8958,1866.9678,464.0625,1856.5511,459.8958,1860.7178" style="stroke:#2C3E50;stroke-width:1.0416666666666667;"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="368.75" x2="352.6042" y1="1806.4209" y2="1806.4209"/><polygon fill="#2C3E50" points="348.4375,1945.7764,352.6042,1956.193,356.7708,1945.7764,352.6042,1949.943" style="stroke:#2C3E50;stroke-width:3.125;"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="352.6042" x2="352.6042" y1="1806.4209" y2="2064.6566"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="352.6042" x2="447.3958" y1="2064.6566" y2="2064.6566"/><polygon fill="#2C3E50" points="436.9792,2060.4899,447.3958,2064.6566,436.9792,2068.8232,441.1458,2064.6566" style="stroke:#2C3E50;stroke-width:1.0416666666666667;"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="459.8958" x2="459.8958" y1="1762.5244" y2="1785.5225"/><polygon fill="#2C3E50" points="455.7292,1775.1058,459.8958,1785.5225,464.0625,1775.1058,459.8958,1779.2725" style="stroke:#2C3E50;stroke-width:1.0416666666666667;"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="459.8958" x2="459.8958" y1="2077.1566" y2="2097.9899"/><polygon fill="#2C3E50" points="455.7292,2087.5732,459.8958,2097.9899,464.0625,2087.5732,459.8958,2091.7399" style="stroke:#2C3E50;stroke-width:1.0416666666666667;"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="1005.7292" x2="1005.7292" y1="173.7956" y2="304.1748"/><polygon fill="#2C3E50" points="1001.5625,293.7581,1005.7292,304.1748,1009.8958,293.7581,1005.7292,297.9248" style="stroke:#2C3E50;stroke-width:1.0416666666666667;"/><text fill="#808080" font-family="sans-serif" font-size="41.6667" lengthAdjust="spacing" textLength="291.6667" x="1020.3125" y="235.0586">&#35268;&#21017;&#21046;&#23450;&#19982;&#25490;&#29677;</text><line style="stroke:#2C3E50;stroke-width:3.125;" x1="1005.7292" x2="1005.7292" y1="1444.8161" y2="1475.1546"/><polygon fill="#2C3E50" points="1001.5625,1464.738,1005.7292,1475.1546,1009.8958,1464.738,1005.7292,1468.9046" style="stroke:#2C3E50;stroke-width:1.0416666666666667;"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="1005.7292" x2="1005.7292" y1="1550.415" y2="1591.1702"/><polygon fill="#2C3E50" points="1001.5625,1580.7536,1005.7292,1591.1702,1009.8958,1580.7536,1005.7292,1584.9202" style="stroke:#2C3E50;stroke-width:1.0416666666666667;"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="1859.8958" x2="1859.8958" y1="2451.9043" y2="2492.6595"/><polygon fill="#2C3E50" points="1855.7292,2482.2428,1859.8958,2492.6595,1864.0625,2482.2428,1859.8958,2486.4095" style="stroke:#2C3E50;stroke-width:1.0416666666666667;"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="1005.7292" x2="1005.7292" y1="379.4352" y2="384.6436"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="1005.7292" x2="459.8958" y1="384.6436" y2="384.6436"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="459.8958" x2="459.8958" y1="384.6436" y2="400.2686"/><polygon fill="#2C3E50" points="455.7292,389.8519,459.8958,400.2686,464.0625,389.8519,459.8958,394.0186" style="stroke:#2C3E50;stroke-width:1.0416666666666667;"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="165.1042" x2="165.1042" y1="941.4958" y2="946.7041"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="165.1042" x2="459.8958" y1="946.7041" y2="946.7041"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="459.8958" x2="459.8958" y1="946.7041" y2="962.3291"/><polygon fill="#2C3E50" points="455.7292,951.9124,459.8958,962.3291,464.0625,951.9124,459.8958,956.0791" style="stroke:#2C3E50;stroke-width:1.0416666666666667;"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="459.8958" x2="459.8958" y1="1016.626" y2="1021.8343"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="459.8958" x2="1005.7292" y1="1021.8343" y2="1021.8343"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="1005.7292" x2="1005.7292" y1="1021.8343" y2="1037.4593"/><polygon fill="#2C3E50" points="1001.5625,1027.0426,1005.7292,1037.4593,1009.8958,1027.0426,1005.7292,1031.2093" style="stroke:#2C3E50;stroke-width:1.0416666666666667;"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="1005.7292" x2="1005.7292" y1="1091.7562" y2="1096.9645"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="1005.7292" x2="165.1042" y1="1096.9645" y2="1096.9645"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="165.1042" x2="165.1042" y1="1096.9645" y2="1112.5895"/><polygon fill="#2C3E50" points="160.9375,1102.1729,165.1042,1112.5895,169.2708,1102.1729,165.1042,1106.3395" style="stroke:#2C3E50;stroke-width:1.0416666666666667;"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="165.1042" x2="165.1042" y1="1137.5895" y2="1142.7979"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="165.1042" x2="1567.7083" y1="1142.7979" y2="1142.7979"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="1567.7083" x2="1567.7083" y1="1142.7979" y2="1158.4229"/><polygon fill="#2C3E50" points="1563.5417,1148.0062,1567.7083,1158.4229,1571.875,1148.0062,1567.7083,1152.1729" style="stroke:#2C3E50;stroke-width:1.0416666666666667;"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="459.8958" x2="459.8958" y1="550.6592" y2="618.693"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="459.8958" x2="165.1042" y1="618.693" y2="618.693"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="165.1042" x2="165.1042" y1="618.693" y2="686.5316"/><polygon fill="#2C3E50" points="160.9375,676.1149,165.1042,686.5316,169.2708,676.1149,165.1042,680.2816" style="stroke:#2C3E50;stroke-width:1.0416666666666667;"/><text fill="#808080" font-family="sans-serif" font-size="41.6667" lengthAdjust="spacing" textLength="375" x="474.4792" y="611.9222">&#26085;&#24120;&#32771;&#21220;&#19982;&#24322;&#24120;&#22788;&#29702;</text><line style="stroke:#2C3E50;stroke-width:3.125;" x1="1005.7292" x2="1005.7292" y1="1666.4307" y2="1671.639"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="1005.7292" x2="459.8958" y1="1671.639" y2="1671.639"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="459.8958" x2="459.8958" y1="1671.639" y2="1687.264"/><polygon fill="#2C3E50" points="455.7292,1676.8473,459.8958,1687.264,464.0625,1676.8473,459.8958,1681.014" style="stroke:#2C3E50;stroke-width:1.0416666666666667;"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="459.8958" x2="459.8958" y1="1942.2282" y2="1960.4574"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="459.8958" x2="1005.7292" y1="1960.4574" y2="1960.4574"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="1005.7292" x2="1005.7292" y1="1960.4574" y2="1977.0264"/><polygon fill="#2C3E50" points="1001.5625,1966.6097,1005.7292,1977.0264,1009.8958,1966.6097,1005.7292,1970.7764" style="stroke:#2C3E50;stroke-width:1.0416666666666667;"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="1005.7292" x2="1005.7292" y1="2031.3232" y2="2036.5316"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="1005.7292" x2="459.8958" y1="2036.5316" y2="2036.5316"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="459.8958" x2="459.8958" y1="2036.5316" y2="2052.1566"/><polygon fill="#2C3E50" points="455.7292,2041.7399,459.8958,2052.1566,464.0625,2041.7399,459.8958,2045.9066" style="stroke:#2C3E50;stroke-width:1.0416666666666667;"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="459.8958" x2="459.8958" y1="2152.2868" y2="2157.4951"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="459.8958" x2="1005.7292" y1="2157.4951" y2="2157.4951"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="1005.7292" x2="1005.7292" y1="2157.4951" y2="2173.1201"/><polygon fill="#2C3E50" points="1001.5625,2162.7035,1005.7292,2173.1201,1009.8958,2162.7035,1005.7292,2166.8701" style="stroke:#2C3E50;stroke-width:1.0416666666666667;"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="1567.7083" x2="1567.7083" y1="1233.6833" y2="1301.7171"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="1567.7083" x2="1005.7292" y1="1301.7171" y2="1301.7171"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="1005.7292" x2="1005.7292" y1="1301.7171" y2="1369.5557"/><polygon fill="#2C3E50" points="1001.5625,1359.139,1005.7292,1369.5557,1009.8958,1359.139,1005.7292,1363.3057" style="stroke:#2C3E50;stroke-width:1.0416666666666667;"/><text fill="#808080" font-family="sans-serif" font-size="41.6667" lengthAdjust="spacing" textLength="291.6667" x="1582.2917" y="1294.9463">&#25968;&#25454;&#27719;&#24635;&#19982;&#26680;&#31639;</text><line style="stroke:#2C3E50;stroke-width:3.125;" x1="1859.8958" x2="1859.8958" y1="2546.9564" y2="2552.1647"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="1859.8958" x2="165.1042" y1="2552.1647" y2="2552.1647"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="165.1042" x2="165.1042" y1="2552.1647" y2="2567.7897"/><polygon fill="#2C3E50" points="160.9375,2557.373,165.1042,2567.7897,169.2708,2557.373,165.1042,2561.5397" style="stroke:#2C3E50;stroke-width:1.0416666666666667;"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="1005.7292" x2="1005.7292" y1="2248.3805" y2="2317.1387"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="1005.7292" x2="1859.8958" y1="2317.1387" y2="2317.1387"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="1859.8958" x2="1859.8958" y1="2317.1387" y2="2397.6074"/><polygon fill="#2C3E50" points="1855.7292,2387.1908,1859.8958,2397.6074,1864.0625,2387.1908,1859.8958,2391.3574" style="stroke:#2C3E50;stroke-width:1.0416666666666667;"/><text fill="#808080" font-family="sans-serif" font-size="41.6667" lengthAdjust="spacing" textLength="166.6667" x="1020.3125" y="2310.3678">&#32467;&#26524;&#24212;&#29992;</text><text fill="#2196F3" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="33.3333" x="149.7396" y="133.6914">&#21592;&#24037;</text><text fill="#2196F3" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="66.6667" x="558.3333" y="133.6914">&#29992;&#20154;&#37096;&#38376;</text><text fill="#2196F3" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="83.3333" x="1119.7917" y="133.6914">&#34892;&#25919;&#20154;&#20107;&#37096;</text><text fill="#2196F3" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="29.1667" x="1576.0417" y="133.6914">IT&#32452;</text><text fill="#2196F3" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="50" x="1929.6875" y="133.6914">&#36130;&#21153;&#37096;</text></g></svg>`,
            file3_b6b365: `<svg xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" contentStyleType="text/css" height="2693.75px" preserveAspectRatio="none" style="width:1780px;height:2693px;" version="1.1" viewBox="0 0 1780 2693" width="1780.2083px" zoomAndPan="magnify"><defs><linearGradient id="g1g88pv0nkosuo0" x1="50%" x2="50%" y1="0%" y2="100%"><stop offset="0%" stop-color="#FFFFFF"/><stop offset="100%" stop-color="#FFFFFF"/></linearGradient></defs><g><rect fill="url(#g1g88pv0nkosuo0)" height="72.64" id="_title" rx="10.4167" ry="10.4167" style="stroke:#CCCCCC;stroke-width:1.0416666666666667;" width="226.6927" x="775.1953" y="20.8333"/><text fill="#000000" font-family="sans-serif" font-size="31.25" font-weight="bold" lengthAdjust="spacing" textLength="193.3594" x="791.862" y="69.9402">&#22521;&#35757;&#31649;&#29702;&#27969;&#31243;</text><rect fill="url(#g1g88pv0nkosuo0)" height="41.7806" style="stroke:url(#g1g88pv0nkosuo0);stroke-width:1.0416666666666667;" width="1745.8333" x="15.625" y="105.9733"/><rect fill="#E6F3E6" height="2575.8382" style="stroke:#E6F3E6;stroke-width:1.0416666666666667;" width="475.5208" x="15.625" y="105.9733"/><ellipse cx="304.6875" cy="163.3789" fill="#9C27B0" rx="10.4167" ry="10.4167" style="stroke:#1A78C2;stroke-width:1.0416666666666667;"/><path d="M20.8333,247.0378 L20.8333,362.1419 A0,0 0 0 0 20.8333,362.1419 L172.9167,362.1419 A0,0 0 0 0 172.9167,362.1419 L172.9167,308.7565 L193.75,304.5898 L172.9167,300.4232 L172.9167,257.4544 L162.5,247.0378 L20.8333,247.0378 A0,0 0 0 0 20.8333,247.0378 " fill="#FFFFE0" style="stroke:#2C3E50;stroke-width:1.0416666666666667;"/><path d="M162.5,247.0378 L162.5,257.4544 L172.9167,257.4544 L162.5,247.0378 " fill="#FFFFE0" style="stroke:#2C3E50;stroke-width:1.0416666666666667;"/><text fill="#000000" font-family="sans-serif" font-size="16.6667" font-weight="bold" lengthAdjust="spacing" textLength="75" x="37.5" y="279.9642">&#38656;&#27714;&#26469;&#28304;:</text><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="109.375" x="37.5" y="300.9277">- &#20844;&#21496;&#21457;&#23637;&#25112;&#30053;</text><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="109.375" x="37.5" y="321.8913">- &#37096;&#38376;&#21457;&#23637;&#35745;&#21010;</text><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="109.375" x="37.5" y="342.8548">- &#32489;&#25928;&#35780;&#20272;&#32467;&#26524;</text><rect fill="#FFFFFF" height="54.2969" rx="10.4167" ry="10.4167" style="stroke:#2C3E50;stroke-width:1.0416666666666667;" width="221.875" x="193.75" y="277.4414"/><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="188.5417" x="210.4167" y="311.4095">1. &#25552;&#20986;&#24180;&#24230;/&#23395;&#24230;&#22521;&#35757;&#38656;&#27714;</text><rect fill="#FFFFFF" height="54.2969" rx="10.4167" ry="10.4167" style="stroke:#2C3E50;stroke-width:1.0416666666666667;" width="234.375" x="187.5" y="608.3659"/><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="201.0417" x="204.1667" y="642.334">5. &#23457;&#26680;&#35745;&#21010;&#33609;&#26696;&#19982;&#21453;&#39304;&#24847;&#35265;</text><rect fill="none" height="434.2041" rx="10.4167" ry="10.4167" style="stroke:#A9A9A9;stroke-width:1.0416666666666667;" width="231.25" x="189.0625" y="1581.486"/><path d="M286.9792,1581.486 L286.9792,1615.9912 L276.5625,1626.4079 L189.0625,1626.4079 " fill="none" style="stroke:#A9A9A9;stroke-width:1.0416666666666667;"/><text fill="#0000FF" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="66.6667" x="202.6042" y="1610.2458">&#23454;&#26045;&#38454;&#27573;</text><rect fill="#FFFFFF" height="54.2969" rx="10.4167" ry="10.4167" style="stroke:#2C3E50;stroke-width:1.0416666666666667;" width="210.4167" x="199.4792" y="1719.2464"/><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="177.0833" x="216.1458" y="1753.2145">17. &#21592;&#24037;&#25253;&#21517;&#25110;&#30830;&#35748;&#21442;&#21152;</text><rect fill="#FFFFFF" height="54.2969" rx="10.4167" ry="10.4167" style="stroke:#2C3E50;stroke-width:1.0416666666666667;" width="160.4167" x="224.4792" y="1794.3766"/><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="127.0833" x="241.1458" y="1828.3447">18. &#25353;&#26102;&#21442;&#21152;&#22521;&#35757;</text><rect fill="none" height="567.9688" rx="10.4167" ry="10.4167" style="stroke:#A9A9A9;stroke-width:1.0416666666666667;" width="264.5833" x="172.3958" y="2072.1761"/><path d="M270.3125,2072.1761 L270.3125,2106.6813 L259.8958,2117.098 L172.3958,2117.098 " fill="none" style="stroke:#A9A9A9;stroke-width:1.0416666666666667;"/><text fill="#0000FF" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="66.6667" x="185.9375" y="2100.9359">&#25928;&#26524;&#30830;&#35748;</text><rect fill="#FFFFFF" height="54.2969" rx="10.4167" ry="10.4167" style="stroke:#2C3E50;stroke-width:1.0416666666666667;" width="210.4167" x="199.4792" y="2134.8063"/><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="177.0833" x="216.1458" y="2168.7744">21. &#23436;&#25104;&#35757;&#21518;&#27979;&#35797;&#25110;&#20316;&#19994;</text><rect fill="#FFFFFF" height="54.2969" rx="10.4167" ry="10.4167" style="stroke:#2C3E50;stroke-width:1.0416666666666667;" width="243.75" x="182.8125" y="2209.9365"/><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="210.4167" x="199.4792" y="2243.9046">22. &#22635;&#20889;&#12298;&#22521;&#35757;&#25928;&#26524;&#35780;&#20272;&#34920;&#12299;</text><line style="stroke:#9C27B0;stroke-width:2.0833333333333335;" x1="15.625" x2="15.625" y1="105.9733" y2="2681.8115"/><rect fill="#FFF0F5" height="2575.8382" style="stroke:#FFF0F5;stroke-width:1.0416666666666667;" width="268.75" x="491.1458" y="105.9733"/><polygon fill="#F1C40F" points="570.8333,918.3431,676.0417,918.3431,688.5417,939.2415,676.0417,960.14,570.8333,960.14,558.3333,939.2415,570.8333,918.3431" style="stroke:#D35400;stroke-width:1.0416666666666667;"/><text fill="#008000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="33.3333" x="638.0208" y="987.8581">&#36890;&#36807;</text><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="84.375" x="581.25" y="946.0612">9 .&#39044;&#31639;&#23457;&#25209;</text><text fill="#FF0000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="33.3333" x="698.9583" y="925.1628">&#39539;&#22238;</text><rect fill="none" height="567.9688" rx="10.4167" ry="10.4167" style="stroke:#A9A9A9;stroke-width:1.0416666666666667;" width="258.3333" x="497.3958" y="2072.1761"/><path d="M595.3125,2072.1761 L595.3125,2106.6813 L584.8958,2117.098 L497.3958,2117.098 " fill="none" style="stroke:#A9A9A9;stroke-width:1.0416666666666667;"/><text fill="#0000FF" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="66.6667" x="510.9375" y="2100.9359">&#25928;&#26524;&#30830;&#35748;</text><rect fill="#FFFFFF" height="75.2604" rx="10.4167" ry="10.4167" style="stroke:#2C3E50;stroke-width:1.0416666666666667;" width="227.0833" x="509.8958" y="2456.2907"/><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="193.75" x="526.5625" y="2490.2588">25. &#26681;&#25454;&#21512;&#21516;&#19982;&#24635;&#32467;&#25253;&#21578;&#65292;</text><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="133.3333" x="526.5625" y="2511.2223">&#22788;&#29702;&#22521;&#35757;&#36153;&#29992;&#25903;&#20184;</text><line style="stroke:#9C27B0;stroke-width:2.0833333333333335;" x1="491.1458" x2="491.1458" y1="105.9733" y2="2681.8115"/><rect fill="#E6E6FA" height="2575.8382" style="stroke:#E6E6FA;stroke-width:1.0416666666666667;" width="629.1667" x="759.8958" y="105.9733"/><rect fill="#FFFFFF" height="54.2969" rx="10.4167" ry="10.4167" style="stroke:#2C3E50;stroke-width:1.0416666666666667;" width="201.0417" x="916.1458" y="382.9753"/><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="167.7083" x="932.8125" y="416.9434">2. &#27719;&#24635;&#21508;&#37096;&#38376;&#22521;&#35757;&#38656;&#27714;</text><rect fill="#FFFFFF" height="54.2969" rx="10.4167" ry="10.4167" style="stroke:#2C3E50;stroke-width:1.0416666666666667;" width="284.375" x="874.4792" y="458.1055"/><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="251.0417" x="891.1458" y="492.0736">3. &#20998;&#26512;&#38656;&#27714;&#65292;&#33609;&#25311;&#20844;&#21496;&#32423;&#22521;&#35757;&#35745;&#21010;</text><rect fill="#FFFFFF" height="54.2969" rx="10.4167" ry="10.4167" style="stroke:#2C3E50;stroke-width:1.0416666666666667;" width="234.375" x="899.4792" y="533.2357"/><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="201.0417" x="916.1458" y="567.2038">4. &#23558;&#35745;&#21010;&#33609;&#26696;&#20998;&#21457;&#33267;&#21508;&#37096;&#38376;</text><rect fill="#FFFFFF" height="54.2969" rx="10.4167" ry="10.4167" style="stroke:#2C3E50;stroke-width:1.0416666666666667;" width="217.7083" x="907.8125" y="729.3294"/><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="184.375" x="924.4792" y="763.2975">6. &#26681;&#25454;&#21453;&#39304;&#20462;&#35746;&#22521;&#35757;&#35745;&#21010;</text><rect fill="#FFFFFF" height="75.2604" rx="10.4167" ry="10.4167" style="stroke:#2C3E50;stroke-width:1.0416666666666667;" width="251.0417" x="891.1458" y="820.0846"/><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="217.7083" x="907.8125" y="854.0527">8. &#24418;&#25104;&#27491;&#24335;&#12298;&#24180;&#24230;&#22521;&#35757;&#35745;&#21010;&#12299;</text><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="83.3333" x="907.8125" y="875.0163">&#24182;&#25552;&#20132;&#23457;&#25209;</text><polygon fill="#F1C40F" points="1016.6667,683.4961,1029.1667,695.9961,1016.6667,708.4961,1004.1667,695.9961,1016.6667,683.4961" style="stroke:#D35400;stroke-width:1.0416666666666667;"/><rect fill="#FFFFFF" height="54.2969" rx="10.4167" ry="10.4167" style="stroke:#2C3E50;stroke-width:1.0416666666666667;" width="217.7083" x="1167.1875" y="793.5872"/><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="184.375" x="1183.8542" y="827.5553">7. &#26681;&#25454;&#23457;&#25209;&#24847;&#35265;&#35843;&#25972;&#39044;&#31639;</text><rect fill="none" height="475.9928" rx="10.4167" ry="10.4167" style="stroke:#A9A9A9;stroke-width:1.0416666666666667;" width="501.0417" x="766.1458" y="1032.251"/><path d="M864.0625,1032.251 L864.0625,1066.7562 L853.6458,1077.1729 L766.1458,1077.1729 " fill="none" style="stroke:#A9A9A9;stroke-width:1.0416666666666667;"/><text fill="#0000FF" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="66.6667" x="779.6875" y="1061.0107">&#20934;&#22791;&#38454;&#27573;</text><rect fill="#FFFFFF" height="75.2604" rx="10.4167" ry="10.4167" style="stroke:#2C3E50;stroke-width:1.0416666666666667;" width="210.4167" x="911.4583" y="1094.8812"/><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="177.0833" x="928.125" y="1128.8493">10. &#26681;&#25454;&#24050;&#25209;&#20934;&#30340;&#35745;&#21010;&#65292;</text><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="133.3333" x="928.125" y="1149.8128">&#24320;&#22987;&#20934;&#22791;&#22521;&#35757;&#36164;&#28304;</text><polygon fill="#F1C40F" points="915.1042,1193.1396,1118.2292,1193.1396,1130.7292,1214.0381,1118.2292,1234.9365,915.1042,1234.9365,902.6042,1214.0381,915.1042,1193.1396" style="stroke:#D35400;stroke-width:1.0416666666666667;"/><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="182.2917" x="925.5208" y="1220.8577">11.&#26159;&#21542;&#26377;&#26465;&#20214;&#20869;&#37096;&#22521;&#35757;?</text><text fill="#008000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="27.0833" x="865.1042" y="1199.9593">Yes</text><text fill="#FF0000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="20.8333" x="1141.1458" y="1199.9593">No</text><rect fill="#98FB98" height="54.2969" rx="10.4167" ry="10.4167" style="stroke:#2C3E50;stroke-width:1.0416666666666667;" width="210.4167" x="786.9792" y="1255.7699"/><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="177.0833" x="803.6458" y="1289.738">12. &#21327;&#35843;&#20869;&#37096;&#35762;&#24072;&#19982;&#22330;&#22320;</text><rect fill="#FFFFE0" height="54.2969" rx="10.4167" ry="10.4167" style="stroke:#2C3E50;stroke-width:1.0416666666666667;" width="210.4167" x="1035.9375" y="1255.7699"/><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="177.0833" x="1052.6042" y="1289.738">13. &#32852;&#31995;&#22806;&#37096;&#26426;&#26500;&#25110;&#35762;&#24072;</text><rect fill="#FFB6C1" height="54.2969" rx="10.4167" ry="10.4167" style="stroke:#2C3E50;stroke-width:1.0416666666666667;" width="177.0833" x="1052.6042" y="1330.9001"/><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="143.75" x="1069.2708" y="1364.8682">14. &#35810;&#20215;&#19982;&#26041;&#26696;&#27604;&#23545;</text><polygon fill="#F1C40F" points="1016.6667,1470.7438,1029.1667,1483.2438,1016.6667,1495.7438,1004.1667,1483.2438,1016.6667,1470.7438" style="stroke:#D35400;stroke-width:1.0416666666666667;"/><rect fill="none" height="434.2041" rx="10.4167" ry="10.4167" style="stroke:#A9A9A9;stroke-width:1.0416666666666667;" width="231.25" x="901.0417" y="1581.486"/><path d="M998.9583,1581.486 L998.9583,1615.9912 L988.5417,1626.4079 L901.0417,1626.4079 " fill="none" style="stroke:#A9A9A9;stroke-width:1.0416666666666667;"/><text fill="#0000FF" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="66.6667" x="914.5833" y="1610.2458">&#23454;&#26045;&#38454;&#27573;</text><rect fill="#FFFFFF" height="54.2969" rx="10.4167" ry="10.4167" style="stroke:#2C3E50;stroke-width:1.0416666666666667;" width="160.4167" x="936.4583" y="1644.1162"/><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="127.0833" x="953.125" y="1678.0843">16. &#21457;&#24067;&#22521;&#35757;&#36890;&#30693;</text><rect fill="#FFFFFF" height="54.2969" rx="10.4167" ry="10.4167" style="stroke:#2C3E50;stroke-width:1.0416666666666667;" width="193.75" x="919.7917" y="1944.637"/><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="160.4167" x="936.4583" y="1978.6051">20. &#36827;&#34892;&#22521;&#35757;&#36807;&#31243;&#31649;&#29702;</text><rect fill="none" height="567.9688" rx="10.4167" ry="10.4167" style="stroke:#A9A9A9;stroke-width:1.0416666666666667;" width="264.5833" x="884.375" y="2072.1761"/><path d="M982.2917,2072.1761 L982.2917,2106.6813 L971.875,2117.098 L884.375,2117.098 " fill="none" style="stroke:#A9A9A9;stroke-width:1.0416666666666667;"/><text fill="#0000FF" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="66.6667" x="897.9167" y="2100.9359">&#25928;&#26524;&#30830;&#35748;</text><rect fill="#FFFFFF" height="54.2969" rx="10.4167" ry="10.4167" style="stroke:#2C3E50;stroke-width:1.0416666666666667;" width="160.4167" x="936.4583" y="2285.0667"/><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="127.0833" x="953.125" y="2319.0348">23. &#27719;&#24635;&#35780;&#20272;&#32467;&#26524;</text><rect fill="#FFFFFF" height="75.2604" rx="10.4167" ry="10.4167" style="stroke:#2C3E50;stroke-width:1.0416666666666667;" width="227.0833" x="903.125" y="2360.1969"/><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="193.75" x="919.7917" y="2394.165">24. &#23558;&#22521;&#35757;&#35760;&#24405;&#19982;&#35780;&#20272;&#32467;&#26524;</text><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="133.3333" x="919.7917" y="2415.1286">&#24402;&#20837;&#21592;&#24037;&#20010;&#20154;&#26723;&#26696;</text><rect fill="#FFFFFF" height="75.2604" rx="10.4167" ry="10.4167" style="stroke:#2C3E50;stroke-width:1.0416666666666667;" width="233.3333" x="900" y="2552.3844"/><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="160.4167" x="916.6667" y="2586.3525">26. &#23558;&#24635;&#32467;&#25253;&#21578;&#23384;&#26723;&#65292;</text><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="200" x="916.6667" y="2607.3161">&#20316;&#20026;&#19979;&#24180;&#24230;&#35745;&#21010;&#30340;&#21442;&#32771;&#20381;&#25454;</text><ellipse cx="1016.6667" cy="2671.3949" fill="none" rx="10.4167" ry="10.4167" style="stroke:#9C27B0;stroke-width:1.5625;"/><line style="stroke:#9C27B0;stroke-width:2.604166666666667;" x1="1010.2217" x2="1023.1117" y1="2664.9499" y2="2677.8398"/><line style="stroke:#9C27B0;stroke-width:2.604166666666667;" x1="1023.1117" x2="1010.2217" y1="2664.9499" y2="2677.8398"/><line style="stroke:#9C27B0;stroke-width:2.0833333333333335;" x1="759.8958" x2="759.8958" y1="105.9733" y2="2681.8115"/><rect fill="#FFFACD" height="2575.8382" style="stroke:#FFFACD;stroke-width:1.0416666666666667;" width="360.9375" x="1389.0625" y="105.9733"/><rect fill="none" height="475.9928" rx="10.4167" ry="10.4167" style="stroke:#A9A9A9;stroke-width:1.0416666666666667;" width="350.5208" x="1395.3125" y="1032.251"/><path d="M1493.2292,1032.251 L1493.2292,1066.7562 L1482.8125,1077.1729 L1395.3125,1077.1729 " fill="none" style="stroke:#A9A9A9;stroke-width:1.0416666666666667;"/><text fill="#0000FF" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="66.6667" x="1408.8542" y="1061.0107">&#20934;&#22791;&#38454;&#27573;</text><rect fill="#87CEFA" height="54.2969" rx="10.4167" ry="10.4167" style="stroke:#2C3E50;stroke-width:1.0416666666666667;" width="193.75" x="1539.5833" y="1406.0303"/><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="160.4167" x="1556.25" y="1439.9984">15. &#31614;&#35746;&#22521;&#35757;&#26381;&#21153;&#21512;&#21516;</text><rect fill="none" height="434.2041" rx="10.4167" ry="10.4167" style="stroke:#A9A9A9;stroke-width:1.0416666666666667;" width="208.3333" x="1419.2708" y="1581.486"/><path d="M1517.1875,1581.486 L1517.1875,1615.9912 L1506.7708,1626.4079 L1419.2708,1626.4079 " fill="none" style="stroke:#A9A9A9;stroke-width:1.0416666666666667;"/><text fill="#0000FF" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="66.6667" x="1432.8125" y="1610.2458">&#23454;&#26045;&#38454;&#27573;</text><rect fill="#FFFFFF" height="54.2969" rx="10.4167" ry="10.4167" style="stroke:#2C3E50;stroke-width:1.0416666666666667;" width="160.4167" x="1431.7708" y="1869.5068"/><text fill="#000000" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="127.0833" x="1448.4375" y="1903.4749">19. &#25552;&#20379;&#22521;&#35757;&#26381;&#21153;</text><line style="stroke:#9C27B0;stroke-width:2.0833333333333335;" x1="1389.0625" x2="1389.0625" y1="105.9733" y2="2681.8115"/><line style="stroke:#9C27B0;stroke-width:2.0833333333333335;" x1="1750" x2="1750" y1="105.9733" y2="2681.8115"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="304.6875" x2="304.6875" y1="173.7956" y2="277.4414"/><polygon fill="#2C3E50" points="300.5208,267.0247,304.6875,277.4414,308.8542,267.0247,304.6875,271.1914" style="stroke:#2C3E50;stroke-width:1.0416666666666667;"/><text fill="#808080" font-family="sans-serif" font-size="41.6667" lengthAdjust="spacing" textLength="166.6667" x="319.2708" y="235.0586">&#35745;&#21010;&#38454;&#27573;</text><line style="stroke:#2C3E50;stroke-width:3.125;" x1="304.6875" x2="304.6875" y1="1773.5433" y2="1794.3766"/><polygon fill="#2C3E50" points="300.5208,1783.96,304.6875,1794.3766,308.8542,1783.96,304.6875,1788.1266" style="stroke:#2C3E50;stroke-width:1.0416666666666667;"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="304.6875" x2="304.6875" y1="2189.1032" y2="2209.9365"/><polygon fill="#2C3E50" points="300.5208,2199.5199,304.6875,2209.9365,308.8542,2199.5199,304.6875,2203.6865" style="stroke:#2C3E50;stroke-width:1.0416666666666667;"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="1016.6667" x2="1016.6667" y1="437.2721" y2="458.1055"/><polygon fill="#2C3E50" points="1012.5,447.6888,1016.6667,458.1055,1020.8333,447.6888,1016.6667,451.8555" style="stroke:#2C3E50;stroke-width:1.0416666666666667;"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="1016.6667" x2="1016.6667" y1="512.4023" y2="533.2357"/><polygon fill="#2C3E50" points="1012.5,522.819,1016.6667,533.2357,1020.8333,522.819,1016.6667,526.9857" style="stroke:#2C3E50;stroke-width:1.0416666666666667;"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="1016.6667" x2="1016.6667" y1="783.6263" y2="820.0846"/><polygon fill="#2C3E50" points="1012.5,809.668,1016.6667,820.0846,1020.8333,809.668,1016.6667,813.8346" style="stroke:#2C3E50;stroke-width:1.0416666666666667;"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="1016.6667" x2="1016.6667" y1="708.4961" y2="729.3294"/><polygon fill="#2C3E50" points="1012.5,718.9128,1016.6667,729.3294,1020.8333,718.9128,1016.6667,723.0794" style="stroke:#2C3E50;stroke-width:1.0416666666666667;"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="1276.0417" x2="1276.0417" y1="695.9961" y2="793.5872"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="1276.0417" x2="1029.1667" y1="695.9961" y2="695.9961"/><polygon fill="#2C3E50" points="1039.5833,691.8294,1029.1667,695.9961,1039.5833,700.1628,1035.4167,695.9961" style="stroke:#2C3E50;stroke-width:1.0416666666666667;"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="1141.1458" x2="1141.1458" y1="1310.0667" y2="1330.9001"/><polygon fill="#2C3E50" points="1136.9792,1320.4834,1141.1458,1330.9001,1145.3125,1320.4834,1141.1458,1324.6501" style="stroke:#2C3E50;stroke-width:1.0416666666666667;"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="902.6042" x2="892.1875" y1="1214.0381" y2="1214.0381"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="892.1875" x2="892.1875" y1="1214.0381" y2="1255.7699"/><polygon fill="#2C3E50" points="888.0208,1245.3532,892.1875,1255.7699,896.3542,1245.3532,892.1875,1249.5199" style="stroke:#2C3E50;stroke-width:1.0416666666666667;"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="1130.7292" x2="1141.1458" y1="1214.0381" y2="1214.0381"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="1141.1458" x2="1141.1458" y1="1214.0381" y2="1255.7699"/><polygon fill="#2C3E50" points="1136.9792,1245.3532,1141.1458,1255.7699,1145.3125,1245.3532,1141.1458,1249.5199" style="stroke:#2C3E50;stroke-width:1.0416666666666667;"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="892.1875" x2="892.1875" y1="1310.0667" y2="1483.2438"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="892.1875" x2="1004.1667" y1="1483.2438" y2="1483.2438"/><polygon fill="#2C3E50" points="993.75,1479.0771,1004.1667,1483.2438,993.75,1487.4105,997.9167,1483.2438" style="stroke:#2C3E50;stroke-width:1.0416666666666667;"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="1016.6667" x2="1016.6667" y1="1170.1416" y2="1193.1396"/><polygon fill="#2C3E50" points="1012.5,1182.723,1016.6667,1193.1396,1020.8333,1182.723,1016.6667,1186.8896" style="stroke:#2C3E50;stroke-width:1.0416666666666667;"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="1016.6667" x2="1016.6667" y1="1495.7438" y2="1644.1162"/><polygon fill="#2C3E50" points="1012.5,1633.6995,1016.6667,1644.1162,1020.8333,1633.6995,1016.6667,1637.8662" style="stroke:#2C3E50;stroke-width:1.0416666666666667;"/><text fill="#808080" font-family="sans-serif" font-size="41.6667" lengthAdjust="spacing" textLength="166.6667" x="1031.25" y="1569.5068">&#23454;&#26045;&#38454;&#27573;</text><line style="stroke:#2C3E50;stroke-width:3.125;" x1="1016.6667" x2="1016.6667" y1="2339.3636" y2="2360.1969"/><polygon fill="#2C3E50" points="1012.5,2349.7803,1016.6667,2360.1969,1020.8333,2349.7803,1016.6667,2353.9469" style="stroke:#2C3E50;stroke-width:1.0416666666666667;"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="1016.6667" x2="1016.6667" y1="2627.6449" y2="2660.9782"/><polygon fill="#2C3E50" points="1012.5,2650.5615,1016.6667,2660.9782,1020.8333,2650.5615,1016.6667,2654.7282" style="stroke:#2C3E50;stroke-width:1.0416666666666667;"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="304.6875" x2="304.6875" y1="331.7383" y2="365.1693"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="304.6875" x2="1016.6667" y1="365.1693" y2="365.1693"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="1016.6667" x2="1016.6667" y1="365.1693" y2="382.9753"/><polygon fill="#2C3E50" points="1012.5,372.5586,1016.6667,382.9753,1020.8333,372.5586,1016.6667,376.7253" style="stroke:#2C3E50;stroke-width:1.0416666666666667;"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="1016.6667" x2="1016.6667" y1="587.5326" y2="592.7409"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="1016.6667" x2="304.6875" y1="592.7409" y2="592.7409"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="304.6875" x2="304.6875" y1="592.7409" y2="608.3659"/><polygon fill="#2C3E50" points="300.5208,597.9492,304.6875,608.3659,308.8542,597.9492,304.6875,602.1159" style="stroke:#2C3E50;stroke-width:1.0416666666666667;"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="688.5417" x2="1276.0417" y1="939.2415" y2="939.2415"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="1276.0417" x2="1276.0417" y1="847.8841" y2="939.2415"/><polygon fill="#2C3E50" points="1271.875,858.3008,1276.0417,847.8841,1280.2083,858.3008,1276.0417,854.1341" style="stroke:#2C3E50;stroke-width:1.0416666666666667;"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="1016.6667" x2="1016.6667" y1="895.3451" y2="900.5534"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="1016.6667" x2="623.4375" y1="900.5534" y2="900.5534"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="623.4375" x2="623.4375" y1="900.5534" y2="918.3431"/><polygon fill="#2C3E50" points="619.2708,907.9264,623.4375,918.3431,627.6042,907.9264,623.4375,912.0931" style="stroke:#2C3E50;stroke-width:1.0416666666666667;"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="304.6875" x2="304.6875" y1="662.6628" y2="667.8711"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="304.6875" x2="1016.6667" y1="667.8711" y2="667.8711"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="1016.6667" x2="1016.6667" y1="667.8711" y2="683.4961"/><polygon fill="#2C3E50" points="1012.5,673.0794,1016.6667,683.4961,1020.8333,673.0794,1016.6667,677.2461" style="stroke:#2C3E50;stroke-width:1.0416666666666667;"/><line style="stroke:#808080;stroke-width:1.5625;stroke-dasharray:7.0,7.0;" x1="1141.1458" x2="1141.1458" y1="1385.1969" y2="1390.4053"/><line style="stroke:#808080;stroke-width:1.5625;stroke-dasharray:7.0,7.0;" x1="1141.1458" x2="1636.4583" y1="1390.4053" y2="1390.4053"/><line style="stroke:#808080;stroke-width:1.5625;stroke-dasharray:7.0,7.0;" x1="1636.4583" x2="1636.4583" y1="1390.4053" y2="1406.0303"/><polygon fill="#808080" points="1632.2917,1395.6136,1636.4583,1406.0303,1640.625,1395.6136,1636.4583,1399.7803" style="stroke:#808080;stroke-width:1.0416666666666667;"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="1636.4583" x2="1636.4583" y1="1460.3271" y2="1483.2438"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="1636.4583" x2="1029.1667" y1="1483.2438" y2="1483.2438"/><polygon fill="#2C3E50" points="1039.5833,1479.0771,1029.1667,1483.2438,1039.5833,1487.4105,1035.4167,1483.2438" style="stroke:#2C3E50;stroke-width:1.0416666666666667;"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="623.4375" x2="623.4375" y1="960.14" y2="1027.0426"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="623.4375" x2="1016.6667" y1="1027.0426" y2="1027.0426"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="1016.6667" x2="1016.6667" y1="1027.0426" y2="1094.8812"/><polygon fill="#2C3E50" points="1012.5,1084.4645,1016.6667,1094.8812,1020.8333,1084.4645,1016.6667,1088.6312" style="stroke:#2C3E50;stroke-width:1.0416666666666667;"/><text fill="#808080" font-family="sans-serif" font-size="41.6667" lengthAdjust="spacing" textLength="166.6667" x="638.0208" y="1020.2718">&#20934;&#22791;&#38454;&#27573;</text><line style="stroke:#2C3E50;stroke-width:3.125;" x1="1016.6667" x2="1016.6667" y1="1698.4131" y2="1703.6214"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="1016.6667" x2="304.6875" y1="1703.6214" y2="1703.6214"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="304.6875" x2="304.6875" y1="1703.6214" y2="1719.2464"/><polygon fill="#2C3E50" points="300.5208,1708.8298,304.6875,1719.2464,308.8542,1708.8298,304.6875,1712.9964" style="stroke:#2C3E50;stroke-width:1.0416666666666667;"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="304.6875" x2="304.6875" y1="1848.6735" y2="1853.8818"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="304.6875" x2="1511.9792" y1="1853.8818" y2="1853.8818"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="1511.9792" x2="1511.9792" y1="1853.8818" y2="1869.5068"/><polygon fill="#2C3E50" points="1507.8125,1859.0902,1511.9792,1869.5068,1516.1458,1859.0902,1511.9792,1863.2568" style="stroke:#2C3E50;stroke-width:1.0416666666666667;"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="1511.9792" x2="1511.9792" y1="1923.8037" y2="1929.012"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="1511.9792" x2="1016.6667" y1="1929.012" y2="1929.012"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="1016.6667" x2="1016.6667" y1="1929.012" y2="1944.637"/><polygon fill="#2C3E50" points="1012.5,1934.2204,1016.6667,1944.637,1020.8333,1934.2204,1016.6667,1938.387" style="stroke:#2C3E50;stroke-width:1.0416666666666667;"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="304.6875" x2="304.6875" y1="2264.2334" y2="2269.4417"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="304.6875" x2="1016.6667" y1="2269.4417" y2="2269.4417"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="1016.6667" x2="1016.6667" y1="2269.4417" y2="2285.0667"/><polygon fill="#2C3E50" points="1012.5,2274.6501,1016.6667,2285.0667,1020.8333,2274.6501,1016.6667,2278.8167" style="stroke:#2C3E50;stroke-width:1.0416666666666667;"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="1016.6667" x2="1016.6667" y1="2435.4574" y2="2440.6657"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="1016.6667" x2="623.4375" y1="2440.6657" y2="2440.6657"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="623.4375" x2="623.4375" y1="2440.6657" y2="2456.2907"/><polygon fill="#2C3E50" points="619.2708,2445.874,623.4375,2456.2907,627.6042,2445.874,623.4375,2450.0407" style="stroke:#2C3E50;stroke-width:1.0416666666666667;"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="623.4375" x2="623.4375" y1="2531.5511" y2="2536.7594"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="623.4375" x2="1016.6667" y1="2536.7594" y2="2536.7594"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="1016.6667" x2="1016.6667" y1="2536.7594" y2="2552.3844"/><polygon fill="#2C3E50" points="1012.5,2541.9678,1016.6667,2552.3844,1020.8333,2541.9678,1016.6667,2546.1344" style="stroke:#2C3E50;stroke-width:1.0416666666666667;"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="1016.6667" x2="1016.6667" y1="1998.9339" y2="2066.9678"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="1016.6667" x2="304.6875" y1="2066.9678" y2="2066.9678"/><line style="stroke:#2C3E50;stroke-width:3.125;" x1="304.6875" x2="304.6875" y1="2066.9678" y2="2134.8063"/><polygon fill="#2C3E50" points="300.5208,2124.3896,304.6875,2134.8063,308.8542,2124.3896,304.6875,2128.5563" style="stroke:#2C3E50;stroke-width:1.0416666666666667;"/><text fill="#808080" font-family="sans-serif" font-size="41.6667" lengthAdjust="spacing" textLength="166.6667" x="1031.25" y="2060.1969">&#25928;&#26524;&#30830;&#35748;</text><text fill="#2196F3" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="112.5" x="197.1354" y="133.6914">&#29992;&#20154;&#37096;&#38376; / &#21592;&#24037;</text><text fill="#2196F3" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="66.6667" x="592.1875" y="133.6914">&#31649;&#29702;&#20013;&#24515;</text><text fill="#2196F3" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="83.3333" x="1032.8125" y="133.6914">&#20154;&#20107;&#34892;&#25919;&#37096;</text><text fill="#2196F3" font-family="sans-serif" font-size="16.6667" lengthAdjust="spacing" textLength="66.6667" x="1536.1979" y="133.6914">&#22806;&#37096;&#26426;&#26500;</text></g></svg>`,
        };
        let currentActiveTab = 'file1_8a86c4';

        function initTabs() {
            const tabButtons = document.querySelectorAll('.tab-button');
            const tabContents = document.querySelectorAll('.tab-content');
            tabButtons.forEach(button => {
                button.addEventListener('click', () => {
                    const targetTab = button.getAttribute('data-tab');
                    tabButtons.forEach(btn => btn.classList.remove('active'));
                    tabContents.forEach(content => content.classList.remove('active'));
                    button.classList.add('active');
                    document.getElementById(targetTab).classList.add('active');
                    currentActiveTab = targetTab;
                    loadSVG(targetTab);
                });
            });
        }

        function loadSVG(tabName) {
            const container = document.getElementById(`${tabName}-svg`);

            if (svgData[tabName] && svgData[tabName].trim()) {
                container.innerHTML = svgData[tabName];
                const svgElement = container.querySelector('svg');

                if (svgElement) {
                    // 应用当前缩放比例
                    const scaleSlider = document.getElementById('svg-scale');
                    if (scaleSlider) {
                        const scale = parseFloat(scaleSlider.value);
                        svgElement.style.transform = `scale(${scale})`;
                        svgElement.style.transformOrigin = 'center center';
                    }
                }
            } else {
                container.innerHTML = `
                    <div class="error">
                        <h3>SVG 内容未找到</h3>
                        <p>流程图 "${tabName}" 的内容暂时无法显示</p>
                    </div>
                `;
            }
        }

        function exportCurrentTab() {
            const fileName = currentActiveTab || '图片';
            exportToPDF(currentActiveTab, fileName);
        }

        // SVG缩放功能
        function initSVGScale() {
            const scaleSlider = document.getElementById('svg-scale');
            const scaleValue = document.getElementById('scale-value');

            if (scaleSlider && scaleValue) {
                scaleSlider.addEventListener('input', function() {
                    const scale = parseFloat(this.value);
                    scaleValue.textContent = Math.round(scale * 100) + '%';

                    // 应用缩放到当前活动的SVG
                    const currentSvgContainer = document.getElementById(`${currentActiveTab}-svg`);
                    const currentSvg = currentSvgContainer ? currentSvgContainer.querySelector('svg') : null;

                    if (currentSvg) {
                        currentSvg.style.transform = `scale(${scale})`;
                        currentSvg.style.transformOrigin = 'center center';
                    }
                });

                // 初始化显示
                scaleValue.textContent = Math.round(parseFloat(scaleSlider.value) * 100) + '%';
            }
        }

        // 应用缩放到新加载的SVG
        function applySVGScale(svgElement) {
            const scaleSlider = document.getElementById('svg-scale');
            if (scaleSlider && svgElement) {
                const scale = parseFloat(scaleSlider.value);
                svgElement.style.transform = `scale(${scale})`;
                svgElement.style.transformOrigin = 'center center';
            }
        }

        // PDF导出功能
        async function exportToPDF(tabName, fileName) {
            const button = document.getElementById('global-export-btn');
            const originalText = button.textContent;

            try {
                button.textContent = '导出中...';
                button.disabled = true;

                const svgContainer = document.getElementById(`${tabName}-svg`);
                const svgElement = svgContainer.querySelector('svg');

                if (!svgElement) {
                    throw new Error('未找到可导出的图形内容');
                }

                // 检查必要的库是否加载
                if (typeof window.jspdf === 'undefined') {
                    throw new Error('jsPDF 库未加载，请检查网络连接');
                }

                // 获取SVG的尺寸信息
                let svgWidth, svgHeight;

                // 尝试从不同属性获取SVG尺寸
                if (svgElement.viewBox && svgElement.viewBox.baseVal) {
                    svgWidth = svgElement.viewBox.baseVal.width;
                    svgHeight = svgElement.viewBox.baseVal.height;
                } else if (svgElement.getAttribute('width') && svgElement.getAttribute('height')) {
                    svgWidth = parseFloat(svgElement.getAttribute('width').replace(/[^\d.]/g, ''));
                    svgHeight = parseFloat(svgElement.getAttribute('height').replace(/[^\d.]/g, ''));
                } else {
                    // 使用实际渲染尺寸
                    const rect = svgElement.getBoundingClientRect();
                    svgWidth = rect.width || 800;
                    svgHeight = rect.height || 600;
                }

                // 创建PDF，根据SVG尺寸选择页面方向和格式
                const { jsPDF } = window.jspdf;
                const isLandscape = svgWidth > svgHeight;

                // 根据SVG尺寸选择合适的页面格式
                let format = 'a4';
                if (svgWidth > 1200 || svgHeight > 1200) {
                    format = 'a3';
                } else if (svgWidth > 2000 || svgHeight > 2000) {
                    format = 'a2';
                }

                const pdf = new jsPDF({
                    orientation: isLandscape ? 'landscape' : 'portrait',
                    unit: 'pt',
                    format: format
                });

                // 获取PDF页面尺寸
                const pageWidth = pdf.internal.pageSize.getWidth();
                const pageHeight = pdf.internal.pageSize.getHeight();
                const margin = 20; // 边距

                // 计算缩放比例，保持宽高比
                const availableWidth = pageWidth - (margin * 2);
                const availableHeight = pageHeight - (margin * 2);
                const scaleX = availableWidth / svgWidth;
                const scaleY = availableHeight / svgHeight;
                const scale = Math.min(scaleX, scaleY, 1); // 不放大，只缩小

                const scaledWidth = svgWidth * scale;
                const scaledHeight = svgHeight * scale;

                // 居中位置
                const x = (pageWidth - scaledWidth) / 2;
                const y = (pageHeight - scaledHeight) / 2;

                // 获取全局设置的像素密度
                const globalQualityInput = document.getElementById('global-quality');
                const pixelRatio = globalQualityInput ? parseInt(globalQualityInput.value) || 2 : 2;

                // 使用canvg将SVG转换为高质量Canvas
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');

                // 设置用户指定分辨率的Canvas
                canvas.width = scaledWidth * pixelRatio;
                canvas.height = scaledHeight * pixelRatio;

                // 获取SVG字符串
                const svgString = new XMLSerializer().serializeToString(svgElement);

                // 使用canvg渲染SVG到Canvas
                if (typeof canvg !== 'undefined') {
                    // 使用canvg库进行高质量渲染
                    const v = canvg.Canvg.fromString(ctx, svgString, {
                        ignoreDimensions: true,
                        scaleWidth: canvas.width,
                        scaleHeight: canvas.height,
                        enableRedraw: false
                    });

                    await v.start();

                    // 转换为高质量图片数据
                    const imgData = canvas.toDataURL('image/png', 1.0);

                    // 添加到PDF - 使用PDF页面上的缩放尺寸
                    pdf.addImage(imgData, 'PNG', x, y, scaledWidth, scaledHeight);

                    // 保存PDF
                    pdf.save(`${fileName}-流程图.pdf`);

                } else {
                    // 备用方案：使用传统方法
                    console.warn('canvg库未加载，使用备用方案');

                    // 缩放上下文
                    ctx.scale(pixelRatio, pixelRatio);
                    ctx.fillStyle = 'white';
                    ctx.fillRect(0, 0, scaledWidth, scaledHeight);

                    // 创建Image对象加载SVG
                    const img = new Image();
                    const svgBlob = new Blob([svgString], { type: 'image/svg+xml;charset=utf-8' });
                    const svgUrl = URL.createObjectURL(svgBlob);

                    img.onload = function() {
                        ctx.drawImage(img, 0, 0, scaledWidth, scaledHeight);
                        const imgData = canvas.toDataURL('image/png', 1.0);
                        pdf.addImage(imgData, 'PNG', x, y, scaledWidth, scaledHeight);

                        // 保存PDF
                        pdf.save(`${fileName}-流程图.pdf`);

                        URL.revokeObjectURL(svgUrl);
                    };

                    img.onerror = function() {
                        alert('SVG处理失败，请重试');
                        button.textContent = originalText;
                        button.disabled = false;
                        URL.revokeObjectURL(svgUrl);
                    };

                    img.src = svgUrl;
                    return;
                }

                // 添加PDF元数据的函数
                function addPDFMetadata() {
                    // 直接保存，不添加页眉页脚
                    pdf.save(`${fileName}-流程图.pdf`);
                }

                // 添加元数据并保存
                addPDFMetadata();

            } catch (error) {
                console.error('PDF导出失败:', error);
                alert('PDF导出失败: ' + error.message);
                button.textContent = originalText;
                button.disabled = false;
            }
        }

        // 页面初始化
        document.addEventListener('DOMContentLoaded', () => {
            initTabs();
            initSVGScale();
            loadSVG(currentActiveTab);
        });




    </script>
</body>
</html>