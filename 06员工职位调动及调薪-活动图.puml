@startuml

title 员工职位调动及调薪流程

' --- 外观参数设置 (Skinparams) ---
!theme materia
skinparam shadowing false
skinparam defaultFontName "SimSun, NSimSun" 
skinparam defaultFontSize 16
skinparam title {
    FontSize 30
    FontColor Black
}
skinparam activity {
    BorderColor #2C3E50
    BackgroundColor #FFFFFF
    ArrowColor #2C3E50
    FontColor #000000 
    DiamondBorderColor #D35400
    DiamondBackgroundColor #F1C40F
    DiamondFontColor #000000
}
skinparam note {
    BorderColor #2C3E50
    BackgroundColor #FFFFE0
    FontColor #000000
}
skinparam partition {
    BorderColor #A9A9A9
    BackgroundColor Transparent
    FontColor Blue
    FontSize 16
}
skinparam roundcorner 20

' --- 自动编号变量 ---
!$step = 0 
!function $next_step()
  !$step = $step + 1 
  !return $step
!endfunction

' --- 流程开始 ---
|#E6F3E6|用人部门 (新/旧)|
|#E6E6FA|行政人事部|
|#FFFACD|员工本人|
|#FFF0F5|财务部|

|用人部门 (新/旧)|
start

:$next_step(). 发起《员工内部\n异动申请》;
note right
  <b>异动原因可能包括:</b>
  - 内部晋升
  - 部门间平调
  - 项目需求临时调动
end note

:$next_step(). 部门负责人审批;

|行政人事部|
:$next_step(). 接收并审核异动\n申请的合规性;
note right: 检查目标岗位编制、\n调动资格等。

if ($next_step().HR初审是否通过?) then (<color:green>Yes</color>)
    :$next_step(). 与员工及相关部门\n沟通调动意向;
    note right
      <b>沟通要点:</b>
      - 新岗位职责
      - 薪酬调整预期
      - 工作交接安排
    end note
    
    :$next_step(). 组织调动评估/面试\n(如需);
    
    #palegreen:$next_step(). 确定最终调动方案\n与生效日期;
    
else (<color:red>No</color>)
    -[#grey,dashed]->
    #lightgrey:$next_step(). 驳回申请，沟通原因;
    stop
endif

group 薪酬调整与审批
-><size:40><color:grey>薪酬调整与审批</color></size>;
    |行政人事部|
    :$next_step(). 根据新岗位和评估结果，\n拟定《调薪建议函》;
    
    |用人部门 (新/旧)|
    :$next_step(). 审核并确认调薪方案;

    |财务部|
    :$next_step(). 从成本角度审核\n调薪方案;
    note right: 评估对部门及公司\n薪酬成本的影响。

    |行政人事部|
    :$next_step(). 获取各方审批意见，\n生成正式《人事异动通知单》;
end group


group 信息变更与手续办理
-><size:40><color:grey>信息变更与手续办理</color></size>;
    |行政人事部|
    :$next_step(). 与员工签署《岗位/\n薪酬变更确认书》;

    |员工本人|
    :$next_step(). 确认并签署文件;
    
    |行政人事部|
    :$next_step(). 更新员工任职记录;
    floating note right #FFC0CB
      <font color=red><b>核心痛点:</b></font>
      1. 多份纸质文件需要线下流转、签字。
      2. HR需手动更新多个表格或系统中的\n   岗位、部门、薪酬等信息。
      3. 变更信息无法实时同步给相关方。
    end note
    :$next_step(). 将《人事异动通知单》\n抄送各相关部门;
    
    ' --- 并行信息更新 ---
    !$parallel_step = $step + 1
    fork
        |财务部|
        :$parallel_step.a. 根据通知单，更新薪资、\n成本中心等财务信息;
    fork again
        |用人部门 (新/旧)|
        :$parallel_step.b. 安排工作交接与接收;
    end fork
    !$step = $step + 1
end group

|员工本人|
:$next_step(). 在新岗位开始工作;

end

@enduml