@startuml

title 薪酬福利发放流程 (月度)

' --- 外观参数设置 (Skinparams) ---
!theme materia
skinparam shadowing false
skinparam defaultFontName "SimSun, NSimSun" 
skinparam defaultFontSize 16
skinparam title {
    FontSize 30
    FontColor Black
}
skinparam activity {
    BorderColor #2C3E50
    BackgroundColor #FFFFFF
    ArrowColor #2C3E50
    FontColor #000000 
    DiamondBorderColor #D35400
    DiamondBackgroundColor #F1C40F
    DiamondFontColor #000000
}
skinparam note {
    BorderColor #2C3E50
    BackgroundColor #FFFFE0
    FontColor #000000
}
skinparam partition {
    BorderColor #A9A9A9
    BackgroundColor Transparent
    FontColor Blue
    FontSize 16
}
skinparam roundcorner 20

' --- 自动编号变量 ---
!$step = 0 
!function $next_step()
  !$step = $step + 1 
  !return $step
!endfunction

' --- 定义泳道 (参与部门/角色) ---
|#E0FFFF|公司各职能部门|
|#E6E6FA|人事行政部|

|#FFF0F5|财务部|
|#E6F3E6|管理中心|

|人事行政部|
start

' --- 阶段一: 数据采集与初步核算 ---

-><size:40><color:grey>数据采集与初步核算</color></size>;
    :$next_step(). 发起月度薪酬核算;
    note right
        <b>时间点:</b> 通常为每月固定日期
        (例如: 每月25日)
    end note
    
    ' --- 合并后的并行数据采集 (Rule #15) ---
    :$next_step(). 并行整理考勤、绩效及异动数据;
    note right
      <b>具体数据源:</b>
      - <b>考勤:</b> 迟到、早退、请假、加班等
      - <b>绩效:</b> 最终确认的绩效奖金
      - <b>异动:</b> 新入职、离职、转岗、晋升等
    end note
    
    :$next_step(). 核算初步薪资数据;
    note right
        <b>核心产出:</b> 《月度薪资草案》
        <b>计算内容:</b>
        - 应发工资 (基本工资+津贴+绩效...)
        - 应扣款项 (社保公积金+个税...)
    end note


' --- 阶段二: 审核与确认 (管理闭环) ---
group 审核与确认
-><size:40><color:grey>审核与确认</color></size>;
    |人事行政部|
    :$next_step(). 分发薪资草案至各部门确认;
    
    repeat
        |公司各职能部门|
        :$next_step(). 部门经理确认本部门薪资数据;


        |人事行政部|
        backward:$next_step(). 汇总并协调相关部门修正数据;
        
    repeat while ($next_step() .所有数据是否均已确认无误?) is (<color:red>No</color>) not (<color:green>Yes</color>)
    
    #palegreen:$next_step(). 生成最终薪资报表;

end group


' --- 阶段三: 审批与发放 ---
group 审批与发放
-><size:40><color:grey>审批与发放</color></size>;
    |管理中心|
    :$next_step(). 审批薪资总额;
  
    |财务部|
    :$next_step(). 接收薪资报表，\n准备资金;
    :$next_step(). 执行银行代发;
    floating note right #FFC0CB
        <font color=red><b>核心痛点:</b></font>
        - 手动制作银行代发文件，易出错
        - 数据保密性风险高
    end note
    
    |人事行政部|
    :$next_step(). 制作并发送电子工资条;
    floating note right #FFC0CB
        <font color=red><b>核心痛点:</b></font>
        - 传统方式为打印纸质工资条，耗时耗力
        - 员工查询历史工资不便
    end note
end group

' --- 阶段四: 归档与分析 ---
group 归档与分析
-><size:40><color:grey>归档与分析</color></size>;
    |财务部|
    :$next_step(). 薪资数据入账与归档;
    
    |人事行政部|
    :$next_step(). 制作薪酬成本分析报表;
    note right
        - 各部门人工成本对比
        - 薪酬总额同比、环比分析
    end note
end group

end
@enduml