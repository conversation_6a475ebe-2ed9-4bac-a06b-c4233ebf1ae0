@startuml

title 生产排班计划与发布流程

' --- 外观参数设置 (Skinparams) ---
!theme materia
skinparam shadowing false
skinparam defaultFontName "SimSun, NSimSun"
skinparam defaultFontSize 16
skinparam title {
  FontSize 30
  FontColor Black
}
skinparam activity {
  BorderColor #2C3E50
  BackgroundColor #FFFFFF
  ArrowColor #2C3E50
  FontColor #000000
  DiamondBorderColor #D35400
  DiamondBackgroundColor #F1C40F
  DiamondFontColor #000000
}
skinparam note {
  BorderColor #2C3E50
  BackgroundColor #FFFFE0
  FontColor #000000
}
skinparam partition {
  BorderColor #A9A9A9
  BackgroundColor Transparent
  FontColor Blue
  FontSize 16
}
skinparam roundcorner 20

' --- 自动编号变量 ---
!$step = 0
!function $next_step()
!$step = $step + 1
!return $step
!endfunction

' --- 流程开始 ---

|#E6E6FA|人事行政部|
|#FFDAB9|总经理|
|#E0FFFF|厂部|
|#E6F3E6|生产管理部|
|生产管理部|
start
repeat

  :$next_step(). 编制生产排期;
  note right
    <b>数据来源:</b> 订单总表
    <b>核心字段:</b> 生产单号, 产品类型,
    订单交期, 订单数量, 产线编号, 产能,
    换线时间, 用工人数, 计划开始/结束时间等
  end note

  :$next_step(). 汇总计划总需求工时;
  note right
    - 选取特定时间周期 (如: 8月)
    - 针对<b>每一条产线</b>，
    -  汇总所有生产单的“计划总需求工时”
  end note

  :$next_step(). 换算并分配工时到人;
  note right
    - 将产线的总需求工时，
    - 按标准工时 (如8小时/天) 换算成“总工作人天”
    - 获取该产线所有已任职员工列表
    - 将“总工作人天”均分或按权重分配给员工
  end note

  :$next_step(). 生成生产人员排班计划;
  note right
    - 为每位员工生成具体的上下班时间表
    - 明确区分正常工作时间与计划加班时间
  end note

  :$next_step(). 制定其他人员排班计划;
  note right
    - 依据生产人员的整体排班情况，
    - 为相关的辅助生产岗和管理岗制定配套班表
  end note

  :$next_step(). 核算预估人工成本;
  note right
    - 数据输入:排班计划&员工薪酬数据
    - <b>计算:</b> 产品,产线及整体的预估人工成本
  end note

  backward:$next_step().重新评估;
  |厂部|
repeat while ($next_step().审核计划)is (<color:red>不同意</color>) not (<color:green>同意</color>)
:$next_step(). 审核因素;
note right
  - 排班是否满足生产交期？
  - 计划加班是否合理？
  - 预估人工成本是否在预算内？
end note
|人事行政部|
: $next_step(). 发布排班计划;
note left
  通过公司公告、内部系统、
  或车间看板等方式正式发布
end note

#98FB98: $next_step(). 将排班数据导入考勤系统;
note left
  系统将自动比对打卡数据与排班计划，
  不符者标记为“考勤异常”
end note

stop

' --- 变更控制流程 ---

group 临时变更



  |生产管理部|
  #yellow: $next_step(). 发现异常, 收集数据;
  -><size:40><color:grey>临时变更</color></size>;
  repeat
    :$next_step(). 发起《临时排班\n调整申请》;
    |厂部|
    #yellow: $next_step(). 加注具体原因;
    note right: 需由厂长亲自处理
    |生产管理部|
    backward:$next_step().调整方案;
    |总经理|
  repeat while ($next_step()  .审核申请)is (<color:red>不同意</color>) not (<color:green>同意</color>)

  |人事行政部|
  #palegreen: $next_step(). 将调整计划导入\n考勤系统;
  :$next_step(). 同时通知相关员工;
  stop
  end group
  @enduml