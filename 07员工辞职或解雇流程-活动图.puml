@startuml

title 员工离职与解雇流程

' --- 外观参数设置 (Skinparams) ---
!theme materia
skinparam shadowing false
skinparam defaultFontName "SimSun, NSimSun"
skinparam defaultFontSize 16
skinparam title {
    FontSize 30
    FontColor Black
}
skinparam activity {
    BorderColor #2C3E50
    BackgroundColor #FFFFFF
    ArrowColor #2C3E50
    FontColor #000000
    DiamondBorderColor #D35400
    DiamondBackgroundColor #F1C40F
    DiamondFontColor #000000
}
skinparam note {
    BorderColor #2C3E50
    BackgroundColor #FFFFE0
    FontColor #000000
}
skinparam partition {
    BorderColor #A9A9A9
    BackgroundColor Transparent
    FontColor Blue
    FontSize 16
}
skinparam roundcorner 20

' --- 自动编号变量 ---
!$step = 0
!function $next_step()
  !$step = $step + 1
  !return $step
!endfunction

' --- 定义泳道 (参与部门) ---
|#E6F3E6|离职员工|
|#E6E6FA|用人部门|
|#FFFACD|行政人事部|
|#E0FFFF|IT组|
|#FFF0F5|财务部|
|#F5F5DC|安保后勤部|


' --- 流程开始 ---
|离职员工|
start
if ($next_step(). 员工主动发起?) then (<color:green>Yes</color>)
    :$next_step(). 提交《离职申请表》;
    note right
      <b>关键信息:</b>
      - 期望最后工作日
      - 离职原因
    end note

    |用人部门|
    :$next_step(). 部门主管/经理审批;
    note right: 评估工作交接难度，确认最后工作日

else (<color:red>No</color>)
-[#grey,dashed]->
    |用人部门|
    #lightgrey:$next_step(). 发起《员工解雇建议》;
    note right: 需附带相关绩效或违纪证明材料

    |行政人事部|
    #lightgrey:$next_step(). 审核解雇建议的\n合规性与风险;
    #lightgrey:$next_step(). 与用人部门沟通确认;

endif

' --- 流程汇合点 ---
group 离职手续办理
-><size:40><color:grey>离职手续办理</color></size>;
    |行政人事部|
    :$next_step(). 发起《离职手续办理单》;
    note right: 这是驱动所有后续部门操作的核心单据

    |离职员工|
    :$next_step(). 办理工作交接、\n资产归还与账务清理;
    note right
        <b>具体内容包括:</b>
        - 工作交接: 向指定同事交接项目、文件、客户资源
        - 资产归还: 归还电脑、工牌、钥匙等
        - 账务清理: 清理报销、借款等
    end note

    |用人部门|
    :$next_step(). 确认工作交接完成;
    note right: 在办理单上签字确认

    |安保后勤部|
    :$next_step(). 确认资产回收;
    note right: 在办理单上签字确认

    |财务部|
    :$next_step(). 确认账务清晰;
    note right: 在办理单上签字确认

    |IT组|
    :$next_step(). 禁用系统账号;
    note right
      <b>操作内容:</b>
      - 邮箱账号
      - ERP/HCM系统账号
      - 其他业务系统账号
    end note
end group


group 离职结算与档案管理
-><size:40><color:grey>离职结算与档案管理</color></size>;
    |行政人事部|
    :$next_step(). 进行离职面谈;
    note right: 了解真实离职原因，获取反馈

    :$next_step(). 收集所有已签字的\n《离职手续办理单》;

    :$next_step(). 计算最终薪酬;
    note right
      <b>计算项:</b>
      - 当月工资
      - 绩效奖金
      - 未休年假折算
      - 离职补偿金(如需)
    end note

    |财务部|
    :$next_step(). 审核最终薪酬数据;
    :$next_step(). 在约定日期发放最终薪酬;

    |行政人事部|
    :$next_step(). 出具《离职证明》;
    :$next_step(). 办理社保/公积金转出;
    :$next_step(). 员工人事档案封存;
    floating note right #FFC0CB
        <font color=red><b>核心痛点:</b></font>
        1. 纸质单据流转慢，易丢失。
        2. 跨部门沟通不畅，常有遗漏项(如账号未及时禁用)。
        3. 薪酬计算依赖手工，易出错。
        4. 离职数据分散，难以进行原因分析和趋势预测。
    end note
end group

end

@enduml